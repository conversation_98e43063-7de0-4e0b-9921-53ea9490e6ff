# -------------------- 初始化Isaac <PERSON> 切换中文语言逻辑 --------------------
from isaacsim import SimulationApp
config = {
    "width": 1280,
    "height": 720,
    "headless": False,
    "experience": "./apps/isaacsim.exp.full.kit",
	 # "display_options": 3286,  # 更接近编辑器完整体验
    "extra_args": [
         "--enable", "omni.kit.language.simplified_chinese",
         "--/app/language=zh-CN"
    ],
}
simulation_app = SimulationApp(config)
#----------------------设置中文--------------------------
import carb
settings = carb.settings.get_settings()
settings.set("/persistent/app/locale_id", "zh-CN")

# -------------------- UI交互逻辑 --------------------
import omni.ui as ui
from omni.ui import color as cl
from pathlib import Path
import os
from omni.kit.window.filepicker import FilePickerDialog
from omni.kit.widget.filebrowser import FileBrowserItem
import requests
import threading
class MultimodalUI:
#--------------------UI样式----------------------------
    style_system = {
            "Button": {
                "background_color": cl("#E1E1E1"),
                "border_color": cl("#A6C8F0"),
                "border_width": 0.5,
                "border_radius": 0.5,
                "margin": 3.0,
                "padding": 3.0,
                "height":40
            },
            "Button.Label": {
                "color": cl.black,
            },
            "Button:hovered": {
                "background_color": cl("#E5F1FB"),
                "border_color": cl("#0078D7")
            },
            "Button:pressed": {
                "background_color": cl("#CCE4F7"),
                "border_color": cl("#005499"),
                "border_width": 1.0
            },
            "VStack> Label":{
                "background_color": cl("#CCE4F7"),
                "border_color": cl("#005499"),
                "border_width": 1.0,
                "height":40
            },
            "VStack> StringField":{
                "background_color": cl("#CCE4F7"),
                "border_color": cl("#005499"),
                "border_width": 1.0
            },
            "StringField::field": {
                "background_color": cl.yellow  # 测试字段背景
            },
            "StringField::field:hovered": {
                 "background_color": cl.light_yellow
            },
            "normal": {
                "Label": {
                    "color":  cl("#0051FF"),
                    "background_color": ui.color(0.9, 0.9, 0.9),
                }
            },
             "warning": {
                 "Label": {
                     "color":  cl("#D6D6D6"),
                     "background_color": ui.color(1, 1, 0.8),
                     "font_style": "italic"
                 }
             },
             "error": {
                  "Label": {
                     "color":  cl("#FF0000"),
                    "background_color": ui.color(1, 0.8, 0.8),
                     "font_style": "bold"
                 }
             }
        }
#--------------------UI布局----------------------------    
    def __init__(self):
        self._window = ui.Window("智能助手", width=400, height=500)
        with self._window.frame:
            with ui.VStack(style=self.style_system):
                with ui.HStack():
                    self._TaskQA = ui.Button("空间问答", clicked_fn=lambda:self.SelectTaskType("qa"))
                    self._TaskNav = ui.Button("空间导航", clicked_fn=lambda:self.SelectTaskType("nav"))
                self._TaskLabel=ui.Label("当前任务模式:空间问答", style={"color": cl("#FFFFFF"),"alignment": ui.Alignment.CENTER})
                ui.Line( style={"color": cl("#A6C8F0")})
			    # 1. 图像输入区域
                ui.Label("图像输入区域", style={"color": cl("#FFFFFF")})
                self._image_widget = ui.Image()  # 图像预览
                with ui.HStack():
                      self._upload_btn = ui.Button("上传图片")
                      self._delect_Img_btn=ui.Button("删除图片")
                    # self._upload_btn = ui.Button("上传图片", clicked_fn=self.OpenImage)
                    # self._delect_Img_btn=ui.Button("删除图片",clicked_fn=self.DeleteImg)
                    #暂时不支持截屏
                    # self._capture_btn = ui.Button("截屏", clicked_fn=self.capture_scene)
              
                ui.Line( style={"color": cl("#A6C8F0")})
                  # 2. 文本输入区域
                ui.Label("输入自然语言问题/任务", sstyle={"color": cl("#FFFFFF")})
                self._text_input = ui.StringField(
				    #  style={"font_size": 24},
                     height=60, 
                     multiline=True,
                     placeholder="请输入您的问题..."
                 )
                
                # 3. 提交按钮
                ui.Button("提交", clicked_fn=lambda:self.submit(self.Handle_Response))
                ui.Line( style={"color": cl("#A6C8F0")})
                # 4. 输出区域
                ui.Label(" 回答", style={"color": cl("#FFFFFF"),"alignment": ui.Alignment.CENTER})
                self._output_cot=ui.Label(
                    '',
                    style={"color": cl("#666768")},
                    word_wrap=True,
                    height=50
                )
                self._output_area = ui.Label(
                    '',
                    style={"color":cl("#84FF8A")},
                    word_wrap=True,
                    height=50
                )
                ui.Line( style={"color": cl("#A6C8F0")})
                
        # 状态变量
        self.image_path = None

#--------------打开文件选择器，选取图片并预览---------------------------
    def OpenImage(self):
       # """打开文件选择器"""
        dialog = FilePickerDialog(
            "选择图片",
            apply_button_label="Open",
            click_apply_handler=lambda f, d: self.on_click_apply(dialog, f, d),
            item_filter_options=["Image Files", "All Files"],
            item_filter_fn=lambda item: self.on_filter_item(dialog, item),
        )
        dialog.show(path=os.path.expanduser("~"))

    def on_filter_item(self,dialog: FilePickerDialog, item: FileBrowserItem) -> bool:
        # 可显示所有目录
        if not item or item.is_folder:
           return True
        ext = os.path.splitext(item.path)[1].lower()
        return ext in [".png", ".jpg", ".jpeg", ".bmp", ".gif"]

    def on_click_apply(self,dialog: FilePickerDialog, filename: str, dirname: str):
        # 点击“Open”时触发
        fullpath = os.path.join(dirname, filename)
        self.image_path =fullpath
        print("Selected file:", self.image_path)
        self._image_widget.source_url = fullpath
        dialog.hide()
    def DeleteImg(self):
        self.image_path=""
        self._image_widget.source_url=""
#-----------------------------------------------------------------------
    def SelectTaskType(self,taskType :str):
        self.tasktype=taskType
        self._TaskLabel.text="当前任务模式:空间问答" if taskType == "qa" else "当前任务模式:空间导航"
        print("当前为问答环节" if taskType == "qa" else "当前为导航环节")

    def capture_scene(self):
        """捕获当前3D场景"""
        from omni.isaac.synthetic_utils import SyntheticDataHelper
        import cv2
        
        # 获取当前场景渲染
        data_helper = SyntheticDataHelper()
        screenshot = data_helper.get_image("rgb")
        
        # 保存临时文件
        self.image_path = Path("/tmp/scene_capture.png")
        cv2.imwrite(str(self.image_path), cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR))
        self._display_image(self.image_path)
    
    def _display_image(self, path: Path):
        """在UI中显示图像"""
        import cv2
        img = cv2.imread(str(path))
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        height, width, _ = img.shape
        
        # 更新UI组件
        self._image_widget.set_bytes_data(
            img.tobytes(),
            width,
            height,
            ui.TextureFormat.RGB8
        )
    
#-----------------------网络请求------------------------------
    url = 'http://192.168.1.23:8000/agent-api'
    tasktype='qa'
    def get_file_info(self,file_path):
       if not file_path:
           print("图片为空")
           return '','',''
       # 获取完整文件名（带后缀）
       full_name = os.path.basename(file_path)
        # 分割文件名和后缀
       file_name = os.path.splitext(full_name)[0]  # 不带后缀的文件名
       file_ext = os.path.splitext(full_name)[1]   # 带点的后缀如".txt"
       # 去掉后缀名中的点（可选）
       clean_ext = file_ext[1:] if file_ext.startswith('.') else file_ext
    
       return full_name, file_name, clean_ext

    def submit(self,callback):
        self._output_cot.text = "处理中..."
        self._output_area.text = ""
        fullFileName,name,ext=self.get_file_info(self.image_path)
        instructionText=self._text_input.model.get_value_as_string()
        data={
           'instruction':instructionText,
           'tasktype': self.tasktype
        }
        if not fullFileName:
            files={
                'image':None
            }
        else:
           files={
              'image':(fullFileName, open(self.image_path, 'rb').read(), f'image/{ext}')
            }
        if (self.image_path is None or self.image_path == "") and (instructionText is None or instructionText == ""):
            print("图片和文字都为空")
            self._output_cot.text="请输入您的问题"
            self._output_cot.style=self.style_system["error"]
            return
        self.post_request(self.url,files,data,callback)
    def post_request(self,url, files, data, callback):
        def _task():
            try:
                response = requests.post(url, files=files, data=data)
                if callback:
                      callback(response,None)
            except Exception as e:
                callback(None,str(e))
                print("Request error:", e)

        thread = threading.Thread(target=_task, daemon=True)
        thread.start()
    
    def Handle_Response(self,result,error):
        if error:
            print("请求错误",error)
            self._output_cot.text="服务器异常"
            self._output_cot.style=self.style_system["error"]
        else:
            # 检查响应状态码
            if result.status_code == 200:
               try:
                   json_data = result.json()
                   print('响应 JSON:', json_data)
                   if(self.tasktype=="nav"):
                     answerContent=json_data.get('output',{}).get('retrieval',{}).get('description')
                     self._output_cot.text=''
                     self._output_area.text = answerContent
                   else:
                   # 尝试解析 JSON 响应
                     cotContent=json_data.get('output',{}).get('cot')
                     answerContent=json_data.get('output',{}).get('answer')
                     self._output_cot.text=cotContent
                     self._output_cot.style=self.style_system["warning"]
                     self._output_area.text = answerContent
                   #设置特征点
                   pos=json_data.get('output',{}).get('retrieval',{}).get('pose')
                   print("pos:",pos)    
               except ValueError as e:
                   print(f'请求异常：{e}')
                   self._output_cot.text="JSON解析异常"
                   self._output_cot.style=self.style_system["error"]
            else:
                self._output_cot.text=result.text
                self._output_cot.style=self.style_system["error"]
                print(f'请求失败，状态码：{result.status_code}')


#-------------------异步网络管理---------------------

import time
from concurrent.futures import ThreadPoolExecutor, Future

class AsyncRequestManager:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.request_queue = queue.Queue()
        self.response_queue = queue.Queue()
        self.is_running = True
        
        # 启动后台处理线程
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
    def _worker_loop(self):
        """后台工作线程循环"""
        while self.is_running:
            try:
                # 非阻塞方式检查队列
                try:
                    request_data = self.request_queue.get_nowait()
                    self._process_request(request_data)
                except queue.Empty:
                    time.sleep(0.01)  # 短暂休眠避免CPU占用过高
                    continue
            except Exception as e:
                print(f"工作线程错误: {e}")

ui_app = MultimodalUI()		
		# 保持主循环，直到窗口关闭
while simulation_app.is_running():
    simulation_app.update()

simulation_app.close()