from isaacsim import SimulationApp
config = {"width": 1280, "height": 720, "headless": False}
simulation_app = SimulationApp(config)

import omni.usd
from pxr import UsdGeom, Gf
import omni.graph.core as og
import json, math
import omni.kit.app
omni.kit.app.get_app().get_extension_manager().set_extension_enabled_immediate("omni.anim.graph", True)
import omni.anim
# ===================== 轨迹解析 =====================
def parse_trajectories(json_path):
    with open(json_path, "r") as f:
        data = json.load(f)
    parsed = {}
    for person, info in data.items():
        traj_list = info.get("trajectory", [])
        parsed[person] = []
        for pt in traj_list:
            ts, pos = pt.get("timestamp"), pt.get("position")
            if ts is None or pos is None:
                continue
            parsed[person].append({"timestamp": ts, "position": pos})
    return parsed


# ===================== Animation Graph 创建 =====================
def create_animation_graph(stage, skel_prim_path):
    """
    创建一个 Animation Graph: Idle <-> Walk，通过 blend weight 控制
    """
    graph_path = f"{skel_prim_path}/AnimGraph"
    og.Controller.edit(
        {"graph_path": graph_path, "evaluator_name": "execution"},
        {
            og.Controller.Keys.CREATE_NODES: [
                ("IdleClip", "omni.anim.clip"),
                ("WalkClip", "omni.anim.clip"),
                ("Blend", "omni.anim.blend"),
                ("Skeleton", "omni.anim.skelAnimation"),
            ],
            og.Controller.Keys.CONNECT: [
                ("IdleClip.outputs:animation", "Blend.inputs:clipA"),
                ("WalkClip.outputs:animation", "Blend.inputs:clipB"),
                ("Blend.outputs:animation", "Skeleton.inputs:animation"),
            ],
            og.Controller.Keys.SET_VALUES: [
                ("IdleClip.inputs:clipPath", "/Animations/Idle"),
                ("WalkClip.inputs:clipPath", "/Animations/Walk"),
                ("Blend.inputs:weight", 0.0),  # 初始 Idle
                ("Skeleton.inputs:prim", skel_prim_path),
            ],
        },
    )
    return graph_path


# ===================== Update 回调 =====================
def on_update():
    stage = omni.usd.get_context().get_stage()

    for person_path, state in person_states.items():
        traj, idx = state["traj"], state["idx"]
        if idx >= len(traj) - 1:
            continue

        pt_now, pt_next = traj[idx], traj[idx + 1]
        pos_now = Gf.Vec3f(-pt_now["position"][0], -pt_now["position"][1], pt_now["position"][2])
        pos_next = Gf.Vec3f(-pt_next["position"][0], -pt_next["position"][1], pt_next["position"][2])

        dt = pt_next["timestamp"] - pt_now["timestamp"]
        speed = 0.0
        if dt > 0:
            dist = (pos_next - pos_now).GetLength()
            speed = dist / dt

        # 设置位置
        prim = stage.GetPrimAtPath(person_path)
        xform = UsdGeom.Xformable(prim)
        ops = xform.GetOrderedXformOps()
        if ops:
            ops[0].Set(pos_now)
        else:
            xform.AddTranslateOp().Set(pos_now)

        # 根据速度切换动画 (blend)
        weight = 0.0 if speed < 0.05 else 1.0
        # 平滑插值
        state["blend_val"] = state["blend_val"] * 0.9 + weight * 0.1

        # 更新到 Animation Graph
        graph_path = state["graph"]
        og.Controller.set(og.Controller.Keys.SET_VALUES, {f"{graph_path}/Blend.inputs:weight": state["blend_val"]})

        # 索引 +1
        state["idx"] += 1


# ===================== Main =====================
person_states = {}

def main():
    json_file = "/data/1_LinuxFiles/Simulation/trajectoriesDatas/trajectories.json"
    trajectories = parse_trajectories(json_file)
    stage = omni.usd.get_context().get_stage()

    person_usd = "/data/1_LinuxFiles/Simulation/UsdFiles/F_Medical_01/F_Medical_01.usd"

    for i, (person, traj) in enumerate(trajectories.items()):
        parent_path = f"/World/{person}_trajectory"
        if not stage.GetPrimAtPath(parent_path):
            UsdGeom.Xform.Define(stage, parent_path)

        person_path = f"{parent_path}/person_{i}"
        if not stage.GetPrimAtPath(person_path):
            person_prim = stage.DefinePrim(person_path, "Xform")
            person_prim.GetReferences().AddReference(person_usd)

        # 生成 AnimationGraph
        graph_path = create_animation_graph(stage, person_path)

        person_states[person_path] = {
            "traj": traj,
            "idx": 0,
            "blend_val": 0.0,
            "graph": graph_path,
        }
        print(f"Loaded {person}, {len(traj)} points, graph={graph_path}")


if __name__ == "__main__":
    main()

    while simulation_app.is_running():
        simulation_app.update()
        on_update()

    simulation_app.close()
