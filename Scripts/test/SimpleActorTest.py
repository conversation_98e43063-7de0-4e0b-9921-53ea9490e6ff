"""
简化的Actor测试脚本
用于调试和验证基本功能
"""

from isaacsim import SimulationApp
config = {
    "width": 1280,
    "height": 720,
    "headless": False,
}
simulation_app = SimulationApp(config)

import omni.usd
import omni.kit.app
import carb
import omni.ui as ui
from pxr import UsdGeom, Gf, UsdSkel, Sdf, UsdShade
import math
import random
import time

print("=== 简化Actor测试开始 ===")

def create_simple_environment():
    """创建简单环境"""
    print("创建简单环境...")
    stage = omni.usd.get_context().get_stage()
    
    # 创建地面
    ground_path = "/World/Ground"
    if not stage.GetPrimAtPath(ground_path):
        print("创建地面...")
        ground = UsdGeom.Cube.Define(stage, ground_path)
        ground.GetSizeAttr().Set(10.0)
        
        xform = UsdGeom.Xformable(ground.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, -5))
        xform.AddScaleOp().Set(Gf.Vec3f(1, 1, 0.1))
        print("地面创建完成")
    
    return True

def create_simple_actor(name, position):
    """创建简单Actor"""
    print(f"创建简单Actor: {name}")
    stage = omni.usd.get_context().get_stage()
    
    try:
        # 创建胶囊体
        actor_path = f"/World/{name}"
        capsule = UsdGeom.Capsule.Define(stage, actor_path)
        capsule.GetHeightAttr().Set(1.8)
        capsule.GetRadiusAttr().Set(0.3)
        
        # 设置颜色
        color = [random.uniform(0.3, 1.0) for _ in range(3)]
        material_path = f"{actor_path}_material"
        material = UsdShade.Material.Define(stage, material_path)
        shader = UsdShade.Shader.Define(stage, material_path + "/Shader")
        shader.CreateIdAttr("UsdPreviewSurface")
        shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(*color))
        material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
        UsdShade.MaterialBindingAPI(capsule).Bind(material)
        
        # 设置位置
        xform = UsdGeom.Xformable(capsule.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(position[0], position[1], position[2] + 0.9))
        
        print(f"Actor {name} 创建成功，位置: {position}")
        return capsule.GetPrim()
        
    except Exception as e:
        print(f"创建Actor失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def move_actor(actor_prim, new_position):
    """移动Actor"""
    try:
        xform = UsdGeom.Xformable(actor_prim)
        ops = xform.GetOrderedXformOps()
        if ops:
            ops[0].Set(Gf.Vec3f(new_position[0], new_position[1], new_position[2] + 0.9))
            return True
    except Exception as e:
        print(f"移动Actor失败: {e}")
    return False

def create_test_ui():
    """创建测试UI"""
    window = ui.Window("简单Actor测试", width=300, height=400)
    
    # 存储创建的actors
    actors = {}
    
    def create_actor():
        name = f"TestActor_{len(actors) + 1}"
        pos = [random.uniform(-3, 3), random.uniform(-3, 3), 0]
        actor = create_simple_actor(name, pos)
        if actor:
            actors[name] = {"prim": actor, "position": pos}
            print(f"UI: 成功创建 {name}")
        else:
            print(f"UI: 创建 {name} 失败")
    
    def move_all_actors():
        for name, data in actors.items():
            new_pos = [random.uniform(-3, 3), random.uniform(-3, 3), 0]
            if move_actor(data["prim"], new_pos):
                data["position"] = new_pos
                print(f"UI: 移动 {name} 到 {new_pos}")
    
    def clear_actors():
        stage = omni.usd.get_context().get_stage()
        for name in list(actors.keys()):
            actor_path = f"/World/{name}"
            if stage.GetPrimAtPath(actor_path):
                stage.RemovePrim(actor_path)
        actors.clear()
        print("UI: 清除所有Actors")
    
    with window.frame:
        with ui.VStack(spacing=10):
            ui.Label("简单Actor测试", style={"font_size": 16, "color": 0xFF00AAFF})
            
            ui.Button("创建Actor", clicked_fn=create_actor, width=250)
            ui.Button("移动所有Actors", clicked_fn=move_all_actors, width=250)
            ui.Button("清除所有Actors", clicked_fn=clear_actors, width=250)
            
            ui.Separator()
            
            ui.Label("状态信息:", style={"font_size": 14})
            status_label = ui.Label(f"Actors数量: {len(actors)}", style={"color": 0xFF00FF00})
            
            ui.Separator()
            
            ui.Label("说明:", style={"font_size": 12})
            ui.Label("• 点击'创建Actor'添加新角色")
            ui.Label("• 点击'移动所有Actors'随机移动")
            ui.Label("• 点击'清除所有Actors'删除所有")
    
    return window, actors

def main():
    """主函数"""
    print("开始主函数...")
    
    # 创建环境
    if create_simple_environment():
        print("环境创建成功")
    else:
        print("环境创建失败")
        return
    
    # 创建UI
    print("创建UI...")
    ui_window, actors = create_test_ui()
    print("UI创建完成")
    
    # 创建一个测试Actor
    print("创建测试Actor...")
    test_actor = create_simple_actor("TestActor_Default", [0, 0, 0])
    if test_actor:
        actors["TestActor_Default"] = {"prim": test_actor, "position": [0, 0, 0]}
        print("默认测试Actor创建成功")
    else:
        print("默认测试Actor创建失败")
    
    print("\n=== 测试说明 ===")
    print("1. 应该能看到一个地面和一个胶囊体Actor")
    print("2. 使用UI按钮测试Actor创建和移动")
    print("3. 检查控制台输出确认功能正常")
    print("================")

if __name__ == "__main__":
    main()
    
    # 简单的主循环
    frame_count = 0
    while simulation_app.is_running():
        simulation_app.update()
        
        frame_count += 1
        if frame_count % 300 == 0:  # 每5秒输出一次
            print(f"运行中... 帧数: {frame_count}")

print("程序结束")
simulation_app.close()
