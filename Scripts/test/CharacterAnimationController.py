"""
<PERSON> 人物动画控制示例
使用 IRA (Isaac Replicator Animation) 和 AnimationGraph 控制人物行走和Idle动画
"""

from isaacsim import SimulationApp
config = {
    "width": 1280,
    "height": 720,
    "headless": False,
    "experience": "./apps/isaacsim.exp.full.kit",
    "extra_args": [
        "--enable", "omni.kit.language.simplified_chinese",
        "--/app/language=zh-CN"
    ],
}
simulation_app = SimulationApp(config)

import omni.usd
import omni.kit.app
import carb
import omni.ui as ui
import omni.graph.core as og
import omni.replicator.core as rep
from pxr import UsdGeom, Gf, UsdSkel, Sdf, UsdShade
import math
import numpy as np

# 全局变量
character_controller = None
animation_graph = None
current_animation_state = "idle"
movement_speed = 0.0
character_prim_path = "/World/Character"

class CharacterAnimationController:
    """人物动画控制器"""
    
    def __init__(self, character_path):
        self.character_path = character_path
        self.stage = omni.usd.get_context().get_stage()
        self.animation_state = "idle"
        self.blend_weight = 0.0
        self.target_blend_weight = 0.0
        self.movement_speed = 0.0
        self.position = [0, 0, 0]
        self.target_position = [0, 0, 0]
        self.is_moving = False
        
        # 动画参数
        self.speed_threshold = 0.1  # 速度阈值，超过此值切换到行走动画
        self.blend_smoothing = 0.1  # 动画混合平滑系数
        
    def create_character(self):
        """创建人物角色"""
        try:
            # 尝试加载人物模型
            character_usd_paths = [
                "/data/1_LinuxFiles/Simulation/UsdFiles/F_Medical_01/F_Medical_01.usd",
                "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/F_Businesss_02/F_Businesss_02.usd",
                "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/M_Businesss_01/M_Businesss_01.usd"
            ]
            
            character_prim = None
            for usd_path in character_usd_paths:
                try:
                    print(f"尝试加载人物模型: {usd_path}")
                    character_prim = self.stage.DefinePrim(self.character_path, "Xform")
                    character_prim.GetReferences().AddReference(usd_path)
                    print(f"成功加载人物模型: {usd_path}")
                    break
                except Exception as e:
                    print(f"加载失败: {e}")
                    continue
            
            if not character_prim:
                # 如果无法加载人物模型，创建简单的胶囊体代替
                print("创建简单胶囊体代替人物模型")
                capsule = UsdGeom.Capsule.Define(self.stage, self.character_path)
                capsule.GetHeightAttr().Set(1.8)
                capsule.GetRadiusAttr().Set(0.3)
                
                # 添加材质
                material_path = f"{self.character_path}_material"
                material = UsdShade.Material.Define(self.stage, material_path)
                shader = UsdShade.Shader.Define(self.stage, material_path + "/Shader")
                shader.CreateIdAttr("UsdPreviewSurface")
                shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(0.2, 0.6, 1.0))
                material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
                UsdShade.MaterialBindingAPI(capsule).Bind(material)
                
                character_prim = capsule.GetPrim()
            
            # 设置初始位置
            xform = UsdGeom.Xformable(character_prim)
            xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, 0))
            
            return character_prim
            
        except Exception as e:
            print(f"创建角色时出错: {e}")
            return None
    
    def create_animation_graph(self):
        """创建动画图"""
        try:
            # 创建动画图
            graph_path = f"{self.character_path}/AnimationGraph"
            
            # 检查是否已存在动画图
            if self.stage.GetPrimAtPath(graph_path):
                print("动画图已存在，跳过创建")
                return graph_path
            
            # 使用 OmniGraph 创建动画控制图
            og.Controller.edit(
                {"graph_path": graph_path, "evaluator_name": "execution"},
                {
                    og.Controller.Keys.CREATE_NODES: [
                        ("IdleAnimation", "omni.anim.clip"),
                        ("WalkAnimation", "omni.anim.clip"), 
                        ("BlendNode", "omni.anim.blend"),
                        ("SkeletonOutput", "omni.anim.skelAnimation"),
                        ("SpeedInput", "omni.graph.nodes.ConstantFloat"),
                    ],
                    og.Controller.Keys.CONNECT: [
                        ("IdleAnimation.outputs:animation", "BlendNode.inputs:clipA"),
                        ("WalkAnimation.outputs:animation", "BlendNode.inputs:clipB"),
                        ("BlendNode.outputs:animation", "SkeletonOutput.inputs:animation"),
                        ("SpeedInput.inputs:value", "BlendNode.inputs:weight"),
                    ],
                    og.Controller.Keys.SET_VALUES: [
                        ("IdleAnimation.inputs:clipPath", "/Animations/Idle"),
                        ("WalkAnimation.inputs:clipPath", "/Animations/Walk"),
                        ("BlendNode.inputs:weight", 0.0),  # 初始为Idle
                        ("SkeletonOutput.inputs:prim", self.character_path),
                        ("SpeedInput.inputs:value", 0.0),
                    ],
                },
            )
            
            print(f"动画图创建成功: {graph_path}")
            return graph_path
            
        except Exception as e:
            print(f"创建动画图时出错: {e}")
            # 创建简单的动画状态标记
            character_prim = self.stage.GetPrimAtPath(self.character_path)
            if character_prim:
                if not character_prim.HasAttribute("animationState"):
                    character_prim.CreateAttribute("animationState", Sdf.ValueTypeNames.String)
                character_prim.GetAttribute("animationState").Set("idle")
            return None
    
    def set_animation_state(self, state, speed=1.0):
        """设置动画状态"""
        try:
            if state == "walking":
                self.target_blend_weight = 1.0
                self.animation_state = "walking"
            else:
                self.target_blend_weight = 0.0
                self.animation_state = "idle"
            
            # 更新动画图权重
            graph_path = f"{self.character_path}/AnimationGraph"
            if self.stage.GetPrimAtPath(graph_path):
                og.Controller.set(
                    og.Controller.Keys.SET_VALUES,
                    {f"{graph_path}/SpeedInput.inputs:value": self.target_blend_weight}
                )
            
            # 更新角色属性
            character_prim = self.stage.GetPrimAtPath(self.character_path)
            if character_prim and character_prim.HasAttribute("animationState"):
                character_prim.GetAttribute("animationState").Set(state)
            
            print(f"动画状态切换到: {state}, 权重: {self.target_blend_weight}")
            
        except Exception as e:
            print(f"设置动画状态时出错: {e}")
    
    def move_to_position(self, target_pos):
        """移动到指定位置"""
        self.target_position = target_pos
        self.is_moving = True
        
        # 计算距离和速度
        distance = math.sqrt(
            (target_pos[0] - self.position[0])**2 + 
            (target_pos[1] - self.position[1])**2
        )
        
        if distance > 0.1:
            self.movement_speed = min(2.0, distance * 0.5)  # 限制最大速度
            self.set_animation_state("walking", self.movement_speed)
        else:
            self.movement_speed = 0.0
            self.set_animation_state("idle")
            self.is_moving = False
    
    def update(self, dt):
        """更新角色状态"""
        try:
            # 平滑混合权重
            self.blend_weight += (self.target_blend_weight - self.blend_weight) * self.blend_smoothing
            
            # 如果正在移动，更新位置
            if self.is_moving:
                # 计算移动方向
                dx = self.target_position[0] - self.position[0]
                dy = self.target_position[1] - self.position[1]
                distance = math.sqrt(dx*dx + dy*dy)
                
                if distance > 0.1:
                    # 标准化方向向量
                    dx /= distance
                    dy /= distance
                    
                    # 更新位置
                    move_distance = self.movement_speed * dt
                    self.position[0] += dx * move_distance
                    self.position[1] += dy * move_distance
                    
                    # 更新角色在场景中的位置
                    character_prim = self.stage.GetPrimAtPath(self.character_path)
                    if character_prim:
                        xform = UsdGeom.Xformable(character_prim)
                        ops = xform.GetOrderedXformOps()
                        if ops:
                            ops[0].Set(Gf.Vec3f(self.position[0], self.position[1], self.position[2]))
                        else:
                            xform.AddTranslateOp().Set(Gf.Vec3f(self.position[0], self.position[1], self.position[2]))
                    
                    # 根据速度自动切换动画
                    if self.movement_speed > self.speed_threshold:
                        if self.animation_state != "walking":
                            self.set_animation_state("walking", self.movement_speed)
                    else:
                        if self.animation_state != "idle":
                            self.set_animation_state("idle")
                else:
                    # 到达目标位置
                    self.position[0] = self.target_position[0]
                    self.position[1] = self.target_position[1]
                    self.is_moving = False
                    self.movement_speed = 0.0
                    self.set_animation_state("idle")
                    
        except Exception as e:
            print(f"更新角色状态时出错: {e}")

def create_control_ui():
    """创建控制UI"""
    global character_controller
    
    window = ui.Window("人物动画控制", width=400, height=350)
    
    def set_idle():
        if character_controller:
            character_controller.set_animation_state("idle")
            character_controller.is_moving = False
            character_controller.movement_speed = 0.0
    
    def set_walking():
        if character_controller:
            character_controller.set_animation_state("walking", 1.5)
    
    def move_forward():
        if character_controller:
            target = [character_controller.position[0], character_controller.position[1] + 3, 0]
            character_controller.move_to_position(target)
    
    def move_backward():
        if character_controller:
            target = [character_controller.position[0], character_controller.position[1] - 3, 0]
            character_controller.move_to_position(target)
    
    def move_left():
        if character_controller:
            target = [character_controller.position[0] - 3, character_controller.position[1], 0]
            character_controller.move_to_position(target)
    
    def move_right():
        if character_controller:
            target = [character_controller.position[0] + 3, character_controller.position[1], 0]
            character_controller.move_to_position(target)
    
    def walk_in_circle():
        if character_controller:
            # 创建圆形路径
            center = character_controller.position.copy()
            radius = 5.0
            angle = 0
            
            def move_next():
                nonlocal angle
                if angle < 2 * math.pi:
                    x = center[0] + radius * math.cos(angle)
                    y = center[1] + radius * math.sin(angle)
                    character_controller.move_to_position([x, y, 0])
                    angle += math.pi / 8  # 16个点组成圆
                    # 延迟执行下一步
                    omni.kit.app.get_app().get_update_event_stream().create_subscription_to_pop(
                        lambda dt: move_next() if not character_controller.is_moving else None
                    )
            
            move_next()
    
    with window.frame:
        with ui.VStack(spacing=10):
            ui.Label("人物动画控制面板", style={"font_size": 18, "color": 0xFF00AAFF})
            
            # 动画控制
            ui.Label("动画控制:", style={"font_size": 14})
            with ui.HStack():
                ui.Button("Idle动画", clicked_fn=set_idle, width=120)
                ui.Button("行走动画", clicked_fn=set_walking, width=120)
            
            ui.Separator()
            
            # 移动控制
            ui.Label("移动控制:", style={"font_size": 14})
            with ui.VStack():
                ui.Button("向前移动", clicked_fn=move_forward, width=200)
                with ui.HStack():
                    ui.Button("向左移动", clicked_fn=move_left, width=95)
                    ui.Button("向右移动", clicked_fn=move_right, width=95)
                ui.Button("向后移动", clicked_fn=move_backward, width=200)
            
            ui.Separator()
            
            # 特殊动作
            ui.Label("特殊动作:", style={"font_size": 14})
            ui.Button("圆形行走", clicked_fn=walk_in_circle, width=200)
            
            ui.Separator()
            
            # 状态显示
            ui.Label("当前状态:", style={"font_size": 14})
            status_label = ui.Label("", style={"color": 0xFF00FF00})
            
            # 说明
            ui.Separator()
            ui.Label("说明:", style={"font_size": 12})
            ui.Label("• 点击按钮控制角色动画和移动")
            ui.Label("• 角色会根据移动速度自动切换动画")
            ui.Label("• 支持Idle和行走动画状态")
    
    return window

def setup_keyboard_controls():
    """设置键盘控制"""
    global character_controller

    def on_keyboard_event(event):
        if event.type == carb.input.KeyboardEventType.KEY_PRESS:
            if event.input == carb.input.KeyboardInput.SPACE:
                # 空格键切换Idle/Walking
                if character_controller:
                    if character_controller.animation_state == "idle":
                        character_controller.set_animation_state("walking", 1.0)
                    else:
                        character_controller.set_animation_state("idle")
            elif event.input == carb.input.KeyboardInput.W:
                # W键向前移动
                if character_controller:
                    target = [character_controller.position[0], character_controller.position[1] + 2, 0]
                    character_controller.move_to_position(target)
            elif event.input == carb.input.KeyboardInput.S:
                # S键向后移动
                if character_controller:
                    target = [character_controller.position[0], character_controller.position[1] - 2, 0]
                    character_controller.move_to_position(target)
            elif event.input == carb.input.KeyboardInput.A:
                # A键向左移动
                if character_controller:
                    target = [character_controller.position[0] - 2, character_controller.position[1], 0]
                    character_controller.move_to_position(target)
            elif event.input == carb.input.KeyboardInput.D:
                # D键向右移动
                if character_controller:
                    target = [character_controller.position[0] + 2, character_controller.position[1], 0]
                    character_controller.move_to_position(target)
        return True

    try:
        app_window = omni.kit.app.get_app().get_window()
        keyboard = app_window.get_keyboard()
        keyboard.subscribe_to_keyboard_events(on_keyboard_event)
        print("键盘控制已设置")
    except Exception as e:
        print(f"设置键盘控制时出错: {e}")

def create_demo_environment():
    """创建演示环境"""
    stage = omni.usd.get_context().get_stage()

    # 创建地面
    ground_path = "/World/Ground"
    if not stage.GetPrimAtPath(ground_path):
        ground = UsdGeom.Cube.Define(stage, ground_path)
        ground.GetSizeAttr().Set(20.0)

        # 设置地面位置和缩放
        xform = UsdGeom.Xformable(ground.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, -10))
        xform.AddScaleOp().Set(Gf.Vec3f(1, 1, 0.1))

        # 添加地面材质
        material_path = f"{ground_path}_material"
        material = UsdShade.Material.Define(stage, material_path)
        shader = UsdShade.Shader.Define(stage, material_path + "/Shader")
        shader.CreateIdAttr("UsdPreviewSurface")
        shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(0.5, 0.5, 0.5))
        material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
        UsdShade.MaterialBindingAPI(ground).Bind(material)

    # 添加一些参考物体
    for i in range(4):
        cube_path = f"/World/RefCube_{i}"
        if not stage.GetPrimAtPath(cube_path):
            cube = UsdGeom.Cube.Define(stage, cube_path)
            cube.GetSizeAttr().Set(1.0)

            # 设置位置
            angle = i * math.pi / 2
            x = 8 * math.cos(angle)
            y = 8 * math.sin(angle)
            xform = UsdGeom.Xformable(cube.GetPrim())
            xform.AddTranslateOp().Set(Gf.Vec3f(x, y, 0.5))

            # 添加颜色
            colors = [(1, 0, 0), (0, 1, 0), (0, 0, 1), (1, 1, 0)]
            material_path = f"{cube_path}_material"
            material = UsdShade.Material.Define(stage, material_path)
            shader = UsdShade.Shader.Define(stage, material_path + "/Shader")
            shader.CreateIdAttr("UsdPreviewSurface")
            shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(*colors[i]))
            material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
            UsdShade.MaterialBindingAPI(cube).Bind(material)

def main():
    """主函数"""
    global character_controller

    print("=== Isaac Sim 人物动画控制演示 ===")
    print("功能特性:")
    print("• 使用 IRA 和 AnimationGraph 控制人物动画")
    print("• 支持 Idle 和 Walking 动画状态")
    print("• 自动根据移动速度切换动画")
    print("• UI 控制面板和键盘快捷键")
    print("• 智能动画混合和平滑过渡")
    print()

    # 创建演示环境
    create_demo_environment()

    # 创建角色控制器
    character_controller = CharacterAnimationController(character_prim_path)

    # 创建角色
    character_prim = character_controller.create_character()
    if not character_prim:
        print("创建角色失败")
        return

    # 创建动画图
    animation_graph = character_controller.create_animation_graph()

    # 创建控制UI
    control_window = create_control_ui()

    # 设置键盘控制
    setup_keyboard_controls()

    print("\n=== 控制说明 ===")
    print("UI控制:")
    print("• 使用控制面板按钮控制动画和移动")
    print("键盘控制:")
    print("• 空格键: 切换 Idle/Walking 动画")
    print("• WASD键: 控制角色移动方向")
    print("• W: 向前移动")
    print("• S: 向后移动")
    print("• A: 向左移动")
    print("• D: 向右移动")
    print("==================\n")

    # 设置初始状态
    character_controller.set_animation_state("idle")

if __name__ == "__main__":
    main()

    # 主循环
    dt = 1.0 / 60.0  # 60 FPS

    while simulation_app.is_running():
        simulation_app.update()

        # 更新角色控制器
        if character_controller:
            character_controller.update(dt)

simulation_app.close()
