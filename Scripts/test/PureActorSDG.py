"""
纯Python实现的Actor SDG (Synthetic Data Generation)功能
模拟Isaac Sim中的Actor控制和动画系统
"""

from isaacsim import SimulationApp
config = {
    "width": 1280,
    "height": 720,
    "headless": False,
}
simulation_app = SimulationApp(config)

import omni.usd
import omni.kit.app
import carb
import omni.ui as ui
from pxr import UsdGeom, Gf, UsdSkel, Sdf, UsdShade
import math
import random
import time
import threading

# 全局变量
actors = {}
simulation_running = False
current_frame = 0

class Actor:
    """Actor类 - 模拟人物角色"""
    
    def __init__(self, name, prim_path):
        self.name = name
        self.prim_path = prim_path
        self.stage = omni.usd.get_context().get_stage()
        self.prim = None
        
        # 状态变量
        self.position = [0.0, 0.0, 0.0]
        self.rotation = 0.0
        self.animation_state = "idle"
        self.animation_time = 0.0
        self.animation_speed = 1.0
        
        # 命令队列
        self.command_queue = []
        self.current_command = None
        self.command_start_time = 0.0
        
        # 移动相关
        self.target_position = None
        self.move_speed = 2.0
        self.is_moving = False
        
    def create_visual(self):
        """创建视觉表示"""
        try:
            # 尝试加载人物模型
            character_urls = [
                "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/F_Businesss_02/F_Businesss_02.usd",
                "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/M_Businesss_01/M_Businesss_01.usd"
            ]
            
            for url in character_urls:
                try:
                    print(f"尝试为{self.name}加载模型: {url}")
                    self.prim = self.stage.DefinePrim(self.prim_path, "Xform")
                    self.prim.GetReferences().AddReference(url)
                    
                    # 设置初始位置
                    xform = UsdGeom.Xformable(self.prim)
                    xform.AddTranslateOp().Set(Gf.Vec3f(*self.position))
                    
                    print(f"成功为{self.name}加载模型")
                    break
                    
                except Exception as e:
                    print(f"加载模型失败: {e}")
                    continue
            
            if not self.prim:
                # 创建简单胶囊体
                print(f"为{self.name}创建简单胶囊体")
                capsule = UsdGeom.Capsule.Define(self.stage, self.prim_path)
                capsule.GetHeightAttr().Set(1.8)
                capsule.GetRadiusAttr().Set(0.3)
                
                # 随机颜色
                color = [random.uniform(0.2, 1.0) for _ in range(3)]
                material_path = f"{self.prim_path}_material"
                material = UsdShade.Material.Define(self.stage, material_path)
                shader = UsdShade.Shader.Define(self.stage, material_path + "/Shader")
                shader.CreateIdAttr("UsdPreviewSurface")
                shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(*color))
                material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
                UsdShade.MaterialBindingAPI(capsule).Bind(material)
                
                self.prim = capsule.GetPrim()
                
                # 设置初始位置
                xform = UsdGeom.Xformable(self.prim)
                xform.AddTranslateOp().Set(Gf.Vec3f(self.position[0], self.position[1], self.position[2] + 0.9))
            
            # 添加动画属性
            self.setup_animation_attributes()
            
            return True
            
        except Exception as e:
            print(f"创建{self.name}的视觉表示失败: {e}")
            return False
    
    def setup_animation_attributes(self):
        """设置动画属性"""
        try:
            if not self.prim.HasAttribute("animationState"):
                self.prim.CreateAttribute("animationState", Sdf.ValueTypeNames.String)
            self.prim.GetAttribute("animationState").Set(self.animation_state)
            
            if not self.prim.HasAttribute("animationSpeed"):
                self.prim.CreateAttribute("animationSpeed", Sdf.ValueTypeNames.Float)
            self.prim.GetAttribute("animationSpeed").Set(self.animation_speed)
            
        except Exception as e:
            print(f"设置{self.name}动画属性失败: {e}")
    
    def add_command(self, command):
        """添加命令到队列"""
        self.command_queue.append(command)
        print(f"{self.name} 添加命令: {command}")
    
    def execute_command(self, command):
        """执行单个命令"""
        try:
            parts = command.split()
            if not parts:
                return
            
            action = parts[0].lower()
            
            if action == "idle":
                duration = float(parts[1]) if len(parts) > 1 else 5.0
                self.start_idle(duration)
                
            elif action == "goto":
                if len(parts) >= 4:
                    x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                    self.start_goto([x, y, z])
                    
            elif action == "lookaround":
                duration = float(parts[1]) if len(parts) > 1 else 3.0
                self.start_lookaround(duration)
                
            elif action == "walk":
                # 随机行走
                x = self.position[0] + random.uniform(-5, 5)
                y = self.position[1] + random.uniform(-5, 5)
                self.start_goto([x, y, 0])
            
        except Exception as e:
            print(f"{self.name} 执行命令失败: {e}")
    
    def start_idle(self, duration):
        """开始Idle动画"""
        self.animation_state = "idle"
        self.current_command = {"type": "idle", "duration": duration, "start_time": time.time()}
        self.is_moving = False
        print(f"{self.name} 开始Idle，持续{duration}秒")
    
    def start_goto(self, target_pos):
        """开始移动到目标位置"""
        self.animation_state = "walking"
        self.target_position = target_pos
        self.current_command = {"type": "goto", "target": target_pos, "start_time": time.time()}
        self.is_moving = True
        print(f"{self.name} 开始移动到 {target_pos}")
    
    def start_lookaround(self, duration):
        """开始环顾四周"""
        self.animation_state = "lookaround"
        self.current_command = {"type": "lookaround", "duration": duration, "start_time": time.time()}
        self.is_moving = False
        print(f"{self.name} 开始环顾四周，持续{duration}秒")
    
    def update(self, dt):
        """更新Actor状态"""
        try:
            # 处理当前命令
            if self.current_command:
                self.update_current_command(dt)
            
            # 如果没有当前命令，从队列中取下一个
            elif self.command_queue:
                next_command = self.command_queue.pop(0)
                self.execute_command(next_command)
            
            # 更新位置
            if self.is_moving and self.target_position:
                self.update_movement(dt)
            
            # 更新动画时间
            self.animation_time += dt * self.animation_speed
            
            # 更新USD属性
            self.update_usd_attributes()
            
        except Exception as e:
            print(f"{self.name} 更新失败: {e}")
    
    def update_current_command(self, dt):
        """更新当前命令"""
        if not self.current_command:
            return
        
        cmd = self.current_command
        elapsed = time.time() - cmd["start_time"]
        
        if cmd["type"] == "idle":
            if elapsed >= cmd["duration"]:
                self.current_command = None
                print(f"{self.name} Idle完成")
                
        elif cmd["type"] == "goto":
            # 移动逻辑在update_movement中处理
            if not self.is_moving:
                self.current_command = None
                print(f"{self.name} 移动完成")
                
        elif cmd["type"] == "lookaround":
            if elapsed >= cmd["duration"]:
                self.current_command = None
                self.animation_state = "idle"
                print(f"{self.name} 环顾四周完成")
    
    def update_movement(self, dt):
        """更新移动"""
        if not self.target_position:
            return
        
        # 计算到目标的距离
        dx = self.target_position[0] - self.position[0]
        dy = self.target_position[1] - self.position[1]
        dz = self.target_position[2] - self.position[2]
        distance = math.sqrt(dx*dx + dy*dy + dz*dz)
        
        if distance < 0.1:
            # 到达目标
            self.position = self.target_position.copy()
            self.target_position = None
            self.is_moving = False
            self.animation_state = "idle"
        else:
            # 继续移动
            move_distance = self.move_speed * dt
            if move_distance > distance:
                move_distance = distance
            
            # 标准化方向向量
            dx /= distance
            dy /= distance
            dz /= distance
            
            # 更新位置
            self.position[0] += dx * move_distance
            self.position[1] += dy * move_distance
            self.position[2] += dz * move_distance
    
    def update_usd_attributes(self):
        """更新USD属性"""
        try:
            if self.prim:
                # 更新位置
                xform = UsdGeom.Xformable(self.prim)
                ops = xform.GetOrderedXformOps()
                if ops:
                    z_offset = 0.9 if self.prim.GetTypeName() == "Capsule" else 0
                    ops[0].Set(Gf.Vec3f(self.position[0], self.position[1], self.position[2] + z_offset))
                
                # 更新动画属性
                if self.prim.HasAttribute("animationState"):
                    self.prim.GetAttribute("animationState").Set(self.animation_state)
                
        except Exception as e:
            print(f"{self.name} 更新USD属性失败: {e}")

class ActorSDGManager:
    """Actor SDG管理器"""
    
    def __init__(self):
        self.actors = {}
        self.update_thread = None
        self.running = False
        
    def create_actor(self, name, position=[0, 0, 0]):
        """创建Actor"""
        prim_path = f"/World/{name}"
        actor = Actor(name, prim_path)
        actor.position = position.copy()
        
        if actor.create_visual():
            self.actors[name] = actor
            print(f"成功创建Actor: {name}")
            return actor
        else:
            print(f"创建Actor失败: {name}")
            return None
    
    def send_command(self, actor_name, command):
        """发送命令给指定Actor"""
        if actor_name in self.actors:
            self.actors[actor_name].add_command(command)
        else:
            print(f"Actor不存在: {actor_name}")
    
    def send_command_to_all(self, command):
        """发送命令给所有Actor"""
        for actor in self.actors.values():
            actor.add_command(command)
    
    def start_simulation(self):
        """开始模拟"""
        global simulation_running
        simulation_running = True
        self.running = True
        print("Actor SDG模拟开始")
    
    def stop_simulation(self):
        """停止模拟"""
        global simulation_running
        simulation_running = False
        self.running = False
        print("Actor SDG模拟停止")
    
    def update(self, dt):
        """更新所有Actor"""
        if not self.running:
            return
        
        for actor in self.actors.values():
            actor.update(dt)

def create_control_ui():
    """创建控制UI"""
    global sdg_manager
    
    window = ui.Window("Actor SDG控制", width=450, height=500)
    
    def create_actor():
        name = actor_name_field.model.get_value_as_string()
        if name and name not in sdg_manager.actors:
            # 随机位置
            pos = [random.uniform(-5, 5), random.uniform(-5, 5), 0]
            sdg_manager.create_actor(name, pos)
        else:
            print("Actor名称无效或已存在")
    
    def send_command():
        actor_name = target_actor_field.model.get_value_as_string()
        command = command_field.model.get_value_as_string()
        if actor_name and command:
            if actor_name == "all":
                sdg_manager.send_command_to_all(command)
            else:
                sdg_manager.send_command(actor_name, command)
    
    def start_sim():
        sdg_manager.start_simulation()
    
    def stop_sim():
        sdg_manager.stop_simulation()
    
    def quick_idle():
        sdg_manager.send_command_to_all("Idle 5")
    
    def quick_walk():
        sdg_manager.send_command_to_all("Walk")
    
    def quick_lookaround():
        sdg_manager.send_command_to_all("LookAround 3")
    
    with window.frame:
        with ui.VStack(spacing=10):
            ui.Label("Actor SDG控制面板", style={"font_size": 18, "color": 0xFF00AAFF})
            
            # Actor创建
            ui.Label("创建Actor:", style={"font_size": 14})
            actor_name_field = ui.StringField(width=350)
            actor_name_field.model.set_value("Character_01")
            ui.Button("创建Actor", clicked_fn=create_actor, width=350)
            
            ui.Separator()
            
            # 模拟控制
            ui.Label("模拟控制:", style={"font_size": 14})
            with ui.HStack():
                ui.Button("开始模拟", clicked_fn=start_sim, width=170)
                ui.Button("停止模拟", clicked_fn=stop_sim, width=170)
            
            ui.Separator()
            
            # 命令控制
            ui.Label("命令控制:", style={"font_size": 14})
            ui.Label("目标Actor (或'all'):")
            target_actor_field = ui.StringField(width=350)
            target_actor_field.model.set_value("all")
            
            ui.Label("命令:")
            command_field = ui.StringField(width=350)
            command_field.model.set_value("Idle 10")
            
            ui.Button("发送命令", clicked_fn=send_command, width=350)
            
            ui.Separator()
            
            # 快速命令
            ui.Label("快速命令:", style={"font_size": 14})
            with ui.VStack():
                ui.Button("所有Actor - Idle", clicked_fn=quick_idle, width=350)
                ui.Button("所有Actor - 随机行走", clicked_fn=quick_walk, width=350)
                ui.Button("所有Actor - 环顾四周", clicked_fn=quick_lookaround, width=350)
            
            ui.Separator()
            
            # 状态显示
            ui.Label("状态:", style={"font_size": 14})
            status_label = ui.Label(f"模拟运行: {simulation_running}, Actor数量: {len(sdg_manager.actors) if sdg_manager else 0}", 
                                  style={"color": 0xFF00FF00})
            
            # 说明
            ui.Separator()
            ui.Label("命令格式:", style={"font_size": 12})
            ui.Label("• Idle [duration]: 静止指定时间")
            ui.Label("• GoTo x y z: 移动到指定位置")
            ui.Label("• LookAround [duration]: 环顾四周")
            ui.Label("• Walk: 随机行走")
    
    return window

def create_environment():
    """创建环境"""
    stage = omni.usd.get_context().get_stage()
    
    # 创建地面
    ground_path = "/World/Ground"
    if not stage.GetPrimAtPath(ground_path):
        ground = UsdGeom.Cube.Define(stage, ground_path)
        ground.GetSizeAttr().Set(20.0)
        
        xform = UsdGeom.Xformable(ground.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, -10))
        xform.AddScaleOp().Set(Gf.Vec3f(1, 1, 0.1))
        
        # 添加材质
        material_path = f"{ground_path}_material"
        material = UsdShade.Material.Define(stage, material_path)
        shader = UsdShade.Shader.Define(stage, material_path + "/Shader")
        shader.CreateIdAttr("UsdPreviewSurface")
        shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(0.4, 0.6, 0.4))
        material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
        UsdShade.MaterialBindingAPI(ground).Bind(material)

# 全局管理器
sdg_manager = None

def main():
    """主函数"""
    global sdg_manager
    
    print("=== 纯Python Actor SDG示例 ===")
    print("模拟Isaac Sim的Actor控制功能")
    print()
    
    # 创建环境
    create_environment()
    
    # 创建SDG管理器
    sdg_manager = ActorSDGManager()
    
    # 创建UI
    ui_window = create_control_ui()
    
    print("\n=== Actor SDG控制说明 ===")
    print("1. 输入Actor名称并点击'创建Actor'")
    print("2. 点击'开始模拟'启动系统")
    print("3. 使用命令控制Actor行为")
    print("4. 支持单个Actor或所有Actor控制")
    print("========================")

if __name__ == "__main__":
    main()
    
    # 主循环
    dt = 1.0 / 60.0  # 60 FPS
    last_time = time.time()
    
    while simulation_app.is_running():
        simulation_app.update()
        
        # 更新SDG管理器
        current_time = time.time()
        actual_dt = current_time - last_time
        last_time = current_time
        
        if sdg_manager:
            sdg_manager.update(actual_dt)

simulation_app.close()
