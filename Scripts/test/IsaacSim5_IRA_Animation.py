"""
Isaac Sim 5.0 IRA (Isaacsim.Replicator.Agent) 人物动画控制示例
使用最新的IRA扩展控制人物行走和Idle动画
"""

from isaacsim import SimulationApp
config = {
    "width": 1280,
    "height": 720,
    "headless": False,
}
simulation_app = SimulationApp(config)

import omni.usd
import omni.kit.app
import carb
import omni.ui as ui
from pxr import UsdGeom, Gf, UsdSkel, Sdf, UsdShade
import math
import asyncio
import time

# 启用IRA扩展
extension_manager = omni.kit.app.get_app().get_extension_manager()
try:
    extension_manager.set_extension_enabled_immediate("isaacsim.replicator.agent", True)
    print("成功启用isaacsim.replicator.agent扩展")
except Exception as e:
    print(f"启用IRA扩展失败: {e}")

# 导入IRA模块
try:
    import isaacsim.replicator.agent as ira
    print("成功导入IRA模块")
except ImportError as e:
    print(f"导入IRA模块失败: {e}")
    ira = None

# 全局变量
character_name = "Character_01"
current_animation = "idle"
animation_speed = 1.0
character_spawned = False
simulation_running = False

class IRAAnimationController:
    """使用IRA的动画控制器"""
    
    def __init__(self):
        self.stage = omni.usd.get_context().get_stage()
        self.character_name = character_name
        self.character_prim = None
        self.commands = []
        self.current_command_index = 0
        
    def spawn_character(self):
        """使用IRA生成角色"""
        global character_spawned
        
        try:
            if not ira:
                print("IRA模块不可用，使用备用方法创建角色")
                return self.create_fallback_character()
            
            print("使用IRA生成角色...")
            
            # 使用IRA生成角色
            # 这里使用IRA的角色生成功能
            character_urls = [
                "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/F_Businesss_02/F_Businesss_02.usd",
                "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/M_Businesss_01/M_Businesss_01.usd"
            ]
            
            for url in character_urls:
                try:
                    print(f"尝试加载角色: {url}")
                    
                    # 创建角色prim
                    character_path = f"/World/{self.character_name}"
                    self.character_prim = self.stage.DefinePrim(character_path, "Xform")
                    self.character_prim.GetReferences().AddReference(url)
                    
                    # 设置初始位置
                    xform = UsdGeom.Xformable(self.character_prim)
                    xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, 0))
                    
                    print(f"角色创建成功: {character_path}")
                    character_spawned = True
                    
                    # 设置角色为IRA控制
                    self.setup_ira_control()
                    
                    return self.character_prim
                    
                except Exception as e:
                    print(f"加载角色失败: {e}")
                    continue
            
            # 如果所有URL都失败，使用备用方法
            return self.create_fallback_character()
            
        except Exception as e:
            print(f"IRA生成角色失败: {e}")
            return self.create_fallback_character()
    
    def create_fallback_character(self):
        """备用角色创建方法"""
        global character_spawned
        
        print("创建备用角色（胶囊体）")
        character_path = f"/World/{self.character_name}"
        
        capsule = UsdGeom.Capsule.Define(self.stage, character_path)
        capsule.GetHeightAttr().Set(1.8)
        capsule.GetRadiusAttr().Set(0.3)
        
        # 添加材质
        material_path = f"{character_path}_material"
        material = UsdShade.Material.Define(self.stage, material_path)
        shader = UsdShade.Shader.Define(self.stage, material_path + "/Shader")
        shader.CreateIdAttr("UsdPreviewSurface")
        shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(0.2, 0.6, 1.0))
        material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
        UsdShade.MaterialBindingAPI(capsule).Bind(material)
        
        xform = UsdGeom.Xformable(capsule.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, 0.9))
        
        self.character_prim = capsule.GetPrim()
        character_spawned = True
        
        # 设置动画属性
        self.setup_animation_attributes()
        
        return self.character_prim
    
    def setup_ira_control(self):
        """设置IRA控制"""
        try:
            # 为角色添加IRA控制属性
            if not self.character_prim.HasAttribute("iraControlled"):
                self.character_prim.CreateAttribute("iraControlled", Sdf.ValueTypeNames.Bool)
            self.character_prim.GetAttribute("iraControlled").Set(True)
            
            # 设置角色类型
            if not self.character_prim.HasAttribute("actorType"):
                self.character_prim.CreateAttribute("actorType", Sdf.ValueTypeNames.String)
            self.character_prim.GetAttribute("actorType").Set("character")
            
            print("IRA控制设置完成")
            
        except Exception as e:
            print(f"设置IRA控制失败: {e}")
            self.setup_animation_attributes()
    
    def setup_animation_attributes(self):
        """设置基础动画属性"""
        try:
            # 设置动画状态属性
            if not self.character_prim.HasAttribute("animationState"):
                self.character_prim.CreateAttribute("animationState", Sdf.ValueTypeNames.String)
            self.character_prim.GetAttribute("animationState").Set("idle")
            
            # 设置动画速度属性
            if not self.character_prim.HasAttribute("animationSpeed"):
                self.character_prim.CreateAttribute("animationSpeed", Sdf.ValueTypeNames.Float)
            self.character_prim.GetAttribute("animationSpeed").Set(1.0)
            
            print("基础动画属性设置完成")
            
        except Exception as e:
            print(f"设置动画属性失败: {e}")
    
    def execute_command(self, command):
        """执行IRA命令"""
        global current_animation
        
        try:
            if not character_spawned:
                print("角色未生成，无法执行命令")
                return False
            
            print(f"执行命令: {self.character_name} {command}")
            
            # 解析命令
            parts = command.split()
            if not parts:
                return False
            
            action = parts[0].lower()
            current_animation = action
            
            # 更新角色属性
            if self.character_prim and self.character_prim.HasAttribute("animationState"):
                self.character_prim.GetAttribute("animationState").Set(action)
            
            # 这里可以添加实际的IRA命令执行逻辑
            # 由于IRA的具体API可能需要特定的设置，我们使用属性来模拟
            
            if action == "idle":
                duration = float(parts[1]) if len(parts) > 1 else 5.0
                print(f"角色进入Idle状态，持续{duration}秒")
                
            elif action == "goto":
                if len(parts) >= 4:
                    x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                    print(f"角色移动到位置: ({x}, {y}, {z})")
                    # 这里可以添加实际的移动逻辑
                    
            elif action == "lookaround":
                duration = float(parts[1]) if len(parts) > 1 else 3.0
                print(f"角色环顾四周，持续{duration}秒")
            
            return True
            
        except Exception as e:
            print(f"执行命令失败: {e}")
            return False
    
    def set_animation_state(self, state, speed=1.0):
        """设置动画状态"""
        global current_animation, animation_speed
        
        try:
            current_animation = state
            animation_speed = speed
            
            # 构建命令
            if state == "idle":
                command = f"Idle {speed * 5}"  # Idle持续时间
            elif state == "walk":
                # 随机生成一个目标位置
                import random
                x = random.uniform(-5, 5)
                y = random.uniform(-5, 5)
                command = f"GoTo {x} {y} 0"
            elif state == "lookaround":
                command = f"LookAround {speed * 3}"
            else:
                command = f"Idle 5"
            
            return self.execute_command(command)
            
        except Exception as e:
            print(f"设置动画状态失败: {e}")
            return False

def create_control_ui():
    """创建控制UI"""
    global ira_controller
    
    window = ui.Window("IRA动画控制", width=400, height=350)
    
    def spawn_character():
        if ira_controller:
            ira_controller.spawn_character()
    
    def set_idle():
        if ira_controller:
            ira_controller.set_animation_state("idle", 1.0)
    
    def set_walk():
        if ira_controller:
            ira_controller.set_animation_state("walk", 1.0)
    
    def set_lookaround():
        if ira_controller:
            ira_controller.set_animation_state("lookaround", 1.0)
    
    def execute_custom_command():
        if ira_controller and custom_command_field.model.get_value_as_string():
            command = custom_command_field.model.get_value_as_string()
            ira_controller.execute_command(command)
    
    with window.frame:
        with ui.VStack(spacing=10):
            ui.Label("IRA动画控制面板", style={"font_size": 18, "color": 0xFF00AAFF})
            
            # 角色控制
            ui.Label("角色控制:", style={"font_size": 14})
            ui.Button("生成角色", clicked_fn=spawn_character, width=300)
            
            ui.Separator()
            
            # 动画控制
            ui.Label("动画控制:", style={"font_size": 14})
            with ui.VStack():
                ui.Button("Idle (静止)", clicked_fn=set_idle, width=300)
                ui.Button("Walk (行走)", clicked_fn=set_walk, width=300)
                ui.Button("LookAround (环顾)", clicked_fn=set_lookaround, width=300)
            
            ui.Separator()
            
            # 自定义命令
            ui.Label("自定义命令:", style={"font_size": 14})
            custom_command_field = ui.StringField(width=300)
            custom_command_field.model.set_value("Idle 10")
            ui.Button("执行命令", clicked_fn=execute_custom_command, width=300)
            
            ui.Separator()
            
            # 状态显示
            ui.Label("当前状态:", style={"font_size": 14})
            status_label = ui.Label(f"动画: {current_animation}, 角色已生成: {character_spawned}", 
                                  style={"color": 0xFF00FF00})
            
            # 说明
            ui.Separator()
            ui.Label("说明:", style={"font_size": 12})
            ui.Label("• 首先点击'生成角色'创建角色")
            ui.Label("• 使用按钮控制动画状态")
            ui.Label("• 自定义命令格式: 'Idle 10' 或 'GoTo 5 5 0'")
            ui.Label("• 基于Isaac Sim 5.0 IRA扩展")
    
    return window

def create_ground():
    """创建地面"""
    stage = omni.usd.get_context().get_stage()
    ground_path = "/World/Ground"
    
    if not stage.GetPrimAtPath(ground_path):
        ground = UsdGeom.Cube.Define(stage, ground_path)
        ground.GetSizeAttr().Set(20.0)
        
        xform = UsdGeom.Xformable(ground.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, -10))
        xform.AddScaleOp().Set(Gf.Vec3f(1, 1, 0.1))
        
        # 添加材质
        material_path = f"{ground_path}_material"
        material = UsdShade.Material.Define(stage, material_path)
        shader = UsdShade.Shader.Define(stage, material_path + "/Shader")
        shader.CreateIdAttr("UsdPreviewSurface")
        shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(0.3, 0.7, 0.3))
        material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
        UsdShade.MaterialBindingAPI(ground).Bind(material)

# 全局控制器实例
ira_controller = None

def main():
    """主函数"""
    global ira_controller
    
    print("=== Isaac Sim 5.0 IRA动画控制示例 ===")
    print("使用Isaacsim.Replicator.Agent扩展")
    print()
    
    # 创建地面
    create_ground()
    
    # 创建IRA控制器
    ira_controller = IRAAnimationController()
    
    # 创建UI
    ui_window = create_control_ui()
    
    print("\n=== IRA控制说明 ===")
    print("UI控制:")
    print("• 点击'生成角色'创建角色")
    print("• 使用按钮控制动画状态")
    print("• 输入自定义命令并执行")
    print("命令格式:")
    print("• Idle [duration]: 静止指定时间")
    print("• GoTo x y z: 移动到指定位置")
    print("• LookAround [duration]: 环顾四周")
    print("==================")

if __name__ == "__main__":
    main()
    
    # 主循环
    while simulation_app.is_running():
        simulation_app.update()

simulation_app.close()
