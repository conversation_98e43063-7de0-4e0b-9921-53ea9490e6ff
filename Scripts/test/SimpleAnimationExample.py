"""
简单的Isaac Sim人物动画示例
演示如何使用IRA和AnimationGraph控制人物行走和Idle动画
"""

from isaacsim import SimulationApp
config = {
    "width": 1280,
    "height": 720,
    "headless": False,
}
simulation_app = SimulationApp(config)

import omni.usd
import omni.kit.app
import carb
import omni.ui as ui
from pxr import UsdGeom, Gf, UsdSkel, Sdf, UsdShade
import math

# 启用必要的扩展
extension_manager = omni.kit.app.get_app().get_extension_manager()
try:
    extension_manager.set_extension_enabled_immediate("omni.anim.people", True)
    print("成功启用omni.anim.people扩展")
except Exception as e:
    print(f"启用omni.anim.people扩展失败: {e}")

# 全局变量
character_path = "/World/Character"
animation_graph_path = None
current_state = "idle"
blend_weight = 0.0

def create_character():
    """创建人物角色"""
    stage = omni.usd.get_context().get_stage()
    
    # 尝试加载人物模型
    character_urls = [
        "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/F_Businesss_02/F_Businesss_02.usd",
        "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/M_Businesss_01/M_Businesss_01.usd"
    ]
    
    for url in character_urls:
        try:
            print(f"尝试加载角色: {url}")
            character_prim = stage.DefinePrim(character_path, "Xform")
            character_prim.GetReferences().AddReference(url)
            
            # 设置初始位置
            xform = UsdGeom.Xformable(character_prim)
            xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, 0))
            
            print(f"成功加载角色: {url}")
            return character_prim
        except Exception as e:
            print(f"加载失败: {e}")
            continue
    
    # 如果无法加载人物模型，创建简单替代品
    print("创建简单胶囊体代替")
    capsule = UsdGeom.Capsule.Define(stage, character_path)
    capsule.GetHeightAttr().Set(1.8)
    capsule.GetRadiusAttr().Set(0.3)
    
    xform = UsdGeom.Xformable(capsule.GetPrim())
    xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, 0.9))
    
    return capsule.GetPrim()

def setup_animation_system():
    """设置动画系统 - 使用Isaac Sim 5.0 API"""
    global animation_graph_path, character_prim

    try:
        stage = omni.usd.get_context().get_stage()
        character_prim = stage.GetPrimAtPath(character_path)

        if not character_prim:
            print("角色prim不存在")
            return None

        print("设置动画系统...")

        # 查找SkelRoot
        skel_root = find_skel_root(character_prim)
        target_prim = skel_root if skel_root else character_prim

        # 创建动画属性
        if not target_prim.HasAttribute("animationState"):
            target_prim.CreateAttribute("animationState", Sdf.ValueTypeNames.String)
        target_prim.GetAttribute("animationState").Set("idle")

        if not target_prim.HasAttribute("animationBlendWeight"):
            target_prim.CreateAttribute("animationBlendWeight", Sdf.ValueTypeNames.Float)
        target_prim.GetAttribute("animationBlendWeight").Set(0.0)

        if not target_prim.HasAttribute("animationSpeed"):
            target_prim.CreateAttribute("animationSpeed", Sdf.ValueTypeNames.Float)
        target_prim.GetAttribute("animationSpeed").Set(1.0)

        # 尝试设置SkelAnimation
        setup_skel_animation(target_prim)

        animation_graph_path = str(target_prim.GetPath())
        print(f"动画系统设置成功: {animation_graph_path}")
        return animation_graph_path

    except Exception as e:
        print(f"设置动画系统失败: {e}")
        return None

def find_skel_root(prim):
    """查找骨骼根节点"""
    if prim.GetTypeName() == "SkelRoot":
        return prim

    for child in prim.GetChildren():
        result = find_skel_root(child)
        if result:
            return result

    return None

def setup_skel_animation(target_prim):
    """设置骨骼动画"""
    try:
        # 查找SkelAnimation
        for child in target_prim.GetChildren():
            if child.GetTypeName() == "SkelAnimation":
                # 设置动画剪辑属性
                if not child.HasAttribute("idleClipPath"):
                    child.CreateAttribute("idleClipPath", Sdf.ValueTypeNames.String)
                child.GetAttribute("idleClipPath").Set("/Animations/Idle")

                if not child.HasAttribute("walkClipPath"):
                    child.CreateAttribute("walkClipPath", Sdf.ValueTypeNames.String)
                child.GetAttribute("walkClipPath").Set("/Animations/Walk")

                if not child.HasAttribute("currentClip"):
                    child.CreateAttribute("currentClip", Sdf.ValueTypeNames.String)
                child.GetAttribute("currentClip").Set("idle")

                print("SkelAnimation设置完成")
                return True

        print("未找到SkelAnimation组件")
        return False

    except Exception as e:
        print(f"设置SkelAnimation失败: {e}")
        return False

def set_animation_blend_weight(weight):
    """设置动画混合权重"""
    global blend_weight, animation_graph_path, current_state

    try:
        blend_weight = max(0.0, min(1.0, weight))  # 限制在0-1之间

        if animation_graph_path:
            stage = omni.usd.get_context().get_stage()
            target_prim = stage.GetPrimAtPath(animation_graph_path)

            if target_prim:
                # 更新混合权重属性
                if target_prim.HasAttribute("animationBlendWeight"):
                    target_prim.GetAttribute("animationBlendWeight").Set(blend_weight)

                # 更新状态
                if blend_weight < 0.5:
                    current_state = "idle"
                    animation_state = "idle"
                else:
                    current_state = "walking"
                    animation_state = "walk"

                if target_prim.HasAttribute("animationState"):
                    target_prim.GetAttribute("animationState").Set(animation_state)

                # 更新SkelAnimation
                update_skel_animation(target_prim, animation_state, blend_weight)

                print(f"动画权重设置为: {blend_weight:.2f}, 状态: {current_state}")

    except Exception as e:
        print(f"设置动画权重失败: {e}")

def update_skel_animation(target_prim, animation_state, blend_weight):
    """更新骨骼动画"""
    try:
        for child in target_prim.GetChildren():
            if child.GetTypeName() == "SkelAnimation":
                # 设置当前动画剪辑
                if child.HasAttribute("currentClip"):
                    child.GetAttribute("currentClip").Set(animation_state)

                # 设置混合权重
                if not child.HasAttribute("blendWeight"):
                    child.CreateAttribute("blendWeight", Sdf.ValueTypeNames.Float)
                child.GetAttribute("blendWeight").Set(blend_weight)

                # 设置播放速度
                speed = 1.0 if animation_state == "idle" else max(1.0, blend_weight * 2.0)
                if not child.HasAttribute("playbackRate"):
                    child.CreateAttribute("playbackRate", Sdf.ValueTypeNames.Float)
                child.GetAttribute("playbackRate").Set(speed)

                break

    except Exception as e:
        print(f"更新SkelAnimation失败: {e}")

def create_simple_ui():
    """创建简单的控制UI"""
    window = ui.Window("动画控制", width=300, height=250)
    
    def set_idle():
        set_animation_blend_weight(0.0)
    
    def set_walking():
        set_animation_blend_weight(1.0)
    
    def set_blend_50():
        set_animation_blend_weight(0.5)
    
    with window.frame:
        with ui.VStack(spacing=10):
            ui.Label("人物动画控制", style={"font_size": 16})
            
            # 动画状态按钮
            ui.Label("动画状态:")
            with ui.HStack():
                ui.Button("Idle", clicked_fn=set_idle, width=80)
                ui.Button("混合", clicked_fn=set_blend_50, width=80)
                ui.Button("行走", clicked_fn=set_walking, width=80)
            
            # 权重滑块
            ui.Label("动画混合权重 (0=Idle, 1=Walk):")
            weight_slider = ui.FloatSlider(min=0.0, max=1.0, step=0.01, width=250)
            weight_slider.model.set_value(0.0)
            weight_slider.model.add_value_changed_fn(
                lambda m: set_animation_blend_weight(m.get_value_as_float())
            )
            
            # 状态显示
            ui.Separator()
            status_label = ui.Label(f"当前状态: {current_state}", style={"color": 0xFF00FF00})
            
            # 说明
            ui.Separator()
            ui.Label("说明:")
            ui.Label("• 使用按钮或滑块控制动画")
            ui.Label("• 权重0.0 = 纯Idle动画")
            ui.Label("• 权重1.0 = 纯行走动画")
            ui.Label("• 中间值 = 混合动画")
    
    return window

def setup_simple_keyboard():
    """设置简单的键盘控制"""
    def on_keyboard_event(event):
        if event.type == carb.input.KeyboardEventType.KEY_PRESS:
            if event.input == carb.input.KeyboardInput.KEY_1:
                set_animation_blend_weight(0.0)  # Idle
            elif event.input == carb.input.KeyboardInput.KEY_2:
                set_animation_blend_weight(0.5)  # 混合
            elif event.input == carb.input.KeyboardInput.KEY_3:
                set_animation_blend_weight(1.0)  # 行走
            elif event.input == carb.input.KeyboardInput.SPACE:
                # 空格键切换
                global current_state
                if current_state == "idle":
                    set_animation_blend_weight(1.0)
                else:
                    set_animation_blend_weight(0.0)
        return True

    try:
        # 使用carb输入系统
        input_interface = carb.input.acquire_input_interface()
        keyboard = input_interface.acquire_keyboard_capture()

        def keyboard_handler(event):
            return on_keyboard_event(event)

        input_interface.subscribe_to_keyboard_events(keyboard, keyboard_handler)
        print("键盘控制已设置")
    except Exception as e:
        print(f"设置键盘控制失败: {e}")

def create_ground():
    """创建地面"""
    stage = omni.usd.get_context().get_stage()
    ground_path = "/World/Ground"

    if not stage.GetPrimAtPath(ground_path):
        ground = UsdGeom.Cube.Define(stage, ground_path)
        ground.GetSizeAttr().Set(10.0)

        xform = UsdGeom.Xformable(ground.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, -5))
        xform.AddScaleOp().Set(Gf.Vec3f(1, 1, 0.1))

        # 添加材质
        material_path = f"{ground_path}_material"
        material = UsdShade.Material.Define(stage, material_path)
        shader = UsdShade.Shader.Define(stage, material_path + "/Shader")
        shader.CreateIdAttr("UsdPreviewSurface")
        shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(0.5, 0.5, 0.5))
        material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
        UsdShade.MaterialBindingAPI(ground).Bind(material)

def main():
    """主函数"""
    print("=== 简单动画控制示例 ===")
    print("演示IRA和AnimationGraph的基本用法")
    print()
    
    # 创建地面
    create_ground()
    
    # 创建角色
    character_prim = create_character()
    if not character_prim:
        print("创建角色失败")
        return
    
    # 设置动画系统
    graph_path = setup_animation_system()
    
    # 创建UI
    ui_window = create_simple_ui()
    
    # 设置键盘控制
    setup_simple_keyboard()
    
    print("\n=== 控制说明 ===")
    print("UI控制:")
    print("• 使用按钮快速切换动画状态")
    print("• 使用滑块精确控制混合权重")
    print("键盘控制:")
    print("• 1键: Idle动画")
    print("• 2键: 混合动画")
    print("• 3键: 行走动画")
    print("• 空格键: 切换Idle/行走")
    print("==================")
    
    # 设置初始状态
    set_animation_blend_weight(0.0)

if __name__ == "__main__":
    main()
    
    # 主循环
    while simulation_app.is_running():
        simulation_app.update()

simulation_app.close()
