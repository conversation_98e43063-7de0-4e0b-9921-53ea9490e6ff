"""
Isaac Sim 5.0 人物动画控制示例
使用正确的Isaac Sim 5.0 API控制人物行走和Idle动画
"""

from isaacsim import SimulationApp
config = {
    "width": 1280,
    "height": 720,
    "headless": False,
}
simulation_app = SimulationApp(config)

import omni.usd
import omni.kit.app
import carb
import omni.ui as ui
from pxr import UsdGeom, Gf, UsdSkel, Sdf, UsdShade
import math

# 启用必要的扩展
extension_manager = omni.kit.app.get_app().get_extension_manager()
try:
    extension_manager.set_extension_enabled_immediate("omni.anim.people", True)
    print("成功启用omni.anim.people扩展")
except Exception as e:
    print(f"启用omni.anim.people扩展失败: {e}")

# 全局变量
character_path = "/World/Character"
current_animation = "idle"
animation_speed = 1.0
character_prim = None

def create_character():
    """创建人物角色"""
    global character_prim
    stage = omni.usd.get_context().get_stage()
    
    # 尝试加载人物模型
    character_urls = [
        "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/F_Businesss_02/F_Businesss_02.usd",
        "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/M_Businesss_01/M_Businesss_01.usd"
    ]
    
    for url in character_urls:
        try:
            print(f"尝试加载角色: {url}")
            character_prim = stage.DefinePrim(character_path, "Xform")
            character_prim.GetReferences().AddReference(url)
            
            # 设置初始位置
            xform = UsdGeom.Xformable(character_prim)
            xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, 0))
            
            print(f"成功加载角色: {url}")
            
            # 设置动画属性
            setup_animation_attributes()
            
            return character_prim
        except Exception as e:
            print(f"加载失败: {e}")
            continue
    
    # 如果无法加载人物模型，创建简单替代品
    print("创建简单胶囊体代替")
    capsule = UsdGeom.Capsule.Define(stage, character_path)
    capsule.GetHeightAttr().Set(1.8)
    capsule.GetRadiusAttr().Set(0.3)
    
    # 添加材质
    material_path = f"{character_path}_material"
    material = UsdShade.Material.Define(stage, material_path)
    shader = UsdShade.Shader.Define(stage, material_path + "/Shader")
    shader.CreateIdAttr("UsdPreviewSurface")
    shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(0.2, 0.6, 1.0))
    material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
    UsdShade.MaterialBindingAPI(capsule).Bind(material)
    
    xform = UsdGeom.Xformable(capsule.GetPrim())
    xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, 0.9))
    
    character_prim = capsule.GetPrim()
    setup_animation_attributes()
    
    return character_prim

def setup_animation_attributes():
    """设置动画属性"""
    global character_prim
    
    try:
        # 查找SkelRoot
        skel_root = find_skel_root(character_prim)
        target_prim = skel_root if skel_root else character_prim
        
        # 创建动画状态属性
        if not target_prim.HasAttribute("animationState"):
            target_prim.CreateAttribute("animationState", Sdf.ValueTypeNames.String)
        target_prim.GetAttribute("animationState").Set("idle")
        
        # 创建动画速度属性
        if not target_prim.HasAttribute("animationSpeed"):
            target_prim.CreateAttribute("animationSpeed", Sdf.ValueTypeNames.Float)
        target_prim.GetAttribute("animationSpeed").Set(1.0)
        
        # 创建动画时间属性
        if not target_prim.HasAttribute("animationTime"):
            target_prim.CreateAttribute("animationTime", Sdf.ValueTypeNames.Float)
        target_prim.GetAttribute("animationTime").Set(0.0)
        
        print("动画属性设置完成")
        
    except Exception as e:
        print(f"设置动画属性失败: {e}")

def find_skel_root(prim):
    """查找骨骼根节点"""
    if prim.GetTypeName() == "SkelRoot":
        return prim
    
    for child in prim.GetChildren():
        result = find_skel_root(child)
        if result:
            return result
    
    return None

def set_animation_state(animation_name, speed=1.0):
    """设置动画状态"""
    global current_animation, animation_speed, character_prim
    
    try:
        if not character_prim:
            print("角色未创建")
            return False
        
        current_animation = animation_name
        animation_speed = speed
        
        # 查找目标prim（优先SkelRoot）
        skel_root = find_skel_root(character_prim)
        target_prim = skel_root if skel_root else character_prim
        
        # 设置动画状态
        if target_prim.HasAttribute("animationState"):
            target_prim.GetAttribute("animationState").Set(animation_name)
        
        if target_prim.HasAttribute("animationSpeed"):
            target_prim.GetAttribute("animationSpeed").Set(speed)
        
        # 尝试使用omni.anim.people API
        try_people_animation(target_prim, animation_name, speed)
        
        print(f"动画状态设置为: {animation_name}, 速度: {speed}")
        return True
        
    except Exception as e:
        print(f"设置动画状态失败: {e}")
        return False

def try_people_animation(target_prim, animation_name, speed):
    """尝试使用omni.anim.people设置动画"""
    try:
        # 尝试导入omni.anim.people模块
        import omni.anim.people.core as people_core
        
        # 如果有SkelAnimation，尝试设置动画剪辑
        for child in target_prim.GetChildren():
            if child.GetTypeName() == "SkelAnimation":
                # 设置动画剪辑路径
                if animation_name == "walk":
                    clip_path = "/Animations/Walk"
                elif animation_name == "run":
                    clip_path = "/Animations/Run"
                else:
                    clip_path = "/Animations/Idle"
                
                # 创建或更新动画剪辑属性
                if not child.HasAttribute("clipPath"):
                    child.CreateAttribute("clipPath", Sdf.ValueTypeNames.String)
                child.GetAttribute("clipPath").Set(clip_path)
                
                if not child.HasAttribute("playbackRate"):
                    child.CreateAttribute("playbackRate", Sdf.ValueTypeNames.Float)
                child.GetAttribute("playbackRate").Set(speed)
                
                print(f"设置SkelAnimation: {clip_path}, 速度: {speed}")
                break
        
    except ImportError:
        print("omni.anim.people模块不可用")
    except Exception as e:
        print(f"使用people动画失败: {e}")

def create_control_ui():
    """创建控制UI"""
    window = ui.Window("Isaac Sim 5.0 动画控制", width=350, height=300)
    
    def set_idle():
        set_animation_state("idle", 1.0)
    
    def set_walk():
        set_animation_state("walk", 1.0)
    
    def set_run():
        set_animation_state("run", 1.5)
    
    def set_custom_speed(speed):
        set_animation_state(current_animation, speed)
    
    with window.frame:
        with ui.VStack(spacing=10):
            ui.Label("Isaac Sim 5.0 动画控制", style={"font_size": 18, "color": 0xFF00AAFF})
            
            # 动画状态按钮
            ui.Label("动画状态:", style={"font_size": 14})
            with ui.VStack():
                ui.Button("Idle (静止)", clicked_fn=set_idle, width=250)
                ui.Button("Walk (行走)", clicked_fn=set_walk, width=250)
                ui.Button("Run (跑步)", clicked_fn=set_run, width=250)
            
            ui.Separator()
            
            # 速度控制
            ui.Label("动画播放速度:", style={"font_size": 14})
            speed_slider = ui.FloatSlider(min=0.1, max=3.0, step=0.1, width=300)
            speed_slider.model.set_value(1.0)
            speed_slider.model.add_value_changed_fn(
                lambda m: set_custom_speed(m.get_value_as_float())
            )
            
            ui.Separator()
            
            # 状态显示
            ui.Label("当前状态:", style={"font_size": 14})
            status_label = ui.Label(f"动画: {current_animation}, 速度: {animation_speed:.1f}", 
                                  style={"color": 0xFF00FF00})
            
            # 说明
            ui.Separator()
            ui.Label("说明:", style={"font_size": 12})
            ui.Label("• 点击按钮切换动画状态")
            ui.Label("• 使用滑块调节播放速度")
            ui.Label("• 键盘1-3键快速切换")
            ui.Label("• 空格键切换Idle/Walk")
    
    return window

def setup_keyboard_controls():
    """设置键盘控制"""
    def on_keyboard_event(event):
        if event.type == carb.input.KeyboardEventType.KEY_PRESS:
            if event.input == carb.input.KeyboardInput.KEY_1:
                set_animation_state("idle", 1.0)
            elif event.input == carb.input.KeyboardInput.KEY_2:
                set_animation_state("walk", 1.0)
            elif event.input == carb.input.KeyboardInput.KEY_3:
                set_animation_state("run", 1.5)
            elif event.input == carb.input.KeyboardInput.SPACE:
                # 空格键在idle和walk之间切换
                if current_animation == "idle":
                    set_animation_state("walk", 1.0)
                else:
                    set_animation_state("idle", 1.0)
        return True
    
    try:
        # 使用carb输入系统
        input_interface = carb.input.acquire_input_interface()
        keyboard = input_interface.acquire_keyboard_capture()
        
        def keyboard_handler(event):
            return on_keyboard_event(event)
        
        input_interface.subscribe_to_keyboard_events(keyboard, keyboard_handler)
        print("键盘控制已设置")
    except Exception as e:
        print(f"设置键盘控制失败: {e}")

def create_ground():
    """创建地面"""
    stage = omni.usd.get_context().get_stage()
    ground_path = "/World/Ground"
    
    if not stage.GetPrimAtPath(ground_path):
        ground = UsdGeom.Cube.Define(stage, ground_path)
        ground.GetSizeAttr().Set(10.0)
        
        xform = UsdGeom.Xformable(ground.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, -5))
        xform.AddScaleOp().Set(Gf.Vec3f(1, 1, 0.1))
        
        # 添加材质
        material_path = f"{ground_path}_material"
        material = UsdShade.Material.Define(stage, material_path)
        shader = UsdShade.Shader.Define(stage, material_path + "/Shader")
        shader.CreateIdAttr("UsdPreviewSurface")
        shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(0.5, 0.5, 0.5))
        material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
        UsdShade.MaterialBindingAPI(ground).Bind(material)

def main():
    """主函数"""
    print("=== Isaac Sim 5.0 动画控制示例 ===")
    print("使用Isaac Sim 5.0正确的动画API")
    print()
    
    # 创建地面
    create_ground()
    
    # 创建角色
    character = create_character()
    if not character:
        print("创建角色失败")
        return
    
    # 创建UI
    ui_window = create_control_ui()
    
    # 设置键盘控制
    setup_keyboard_controls()
    
    print("\n=== 控制说明 ===")
    print("UI控制:")
    print("• 使用按钮切换动画状态")
    print("• 使用滑块调节播放速度")
    print("键盘控制:")
    print("• 1键: Idle动画")
    print("• 2键: Walk动画")
    print("• 3键: Run动画")
    print("• 空格键: 切换Idle/Walk")
    print("==================")
    
    # 设置初始动画状态
    set_animation_state("idle", 1.0)

if __name__ == "__main__":
    main()
    
    # 主循环
    while simulation_app.is_running():
        simulation_app.update()

simulation_app.close()
