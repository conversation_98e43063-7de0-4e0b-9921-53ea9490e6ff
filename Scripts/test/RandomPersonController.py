import numpy as np
import carb
from omni.isaac.kit import SimulationApp

# 启动Isaac Sim
simulation_app = SimulationApp({"headless": False})

# 导入必要的Isaac Sim模块
from omni.isaac.core import World
from omni.isaac.core.articulations import Articulation
from omni.isaac.core.utils.types import ArticulationAction
from omni.isaac.core.utils.stage import add_reference_to_stage
from omni.isaac.core.controllers import BaseController

# 假设的轨迹点数据（每秒30个点）
# 实际应用中应该从文件或其他数据源获取
def generate_sample_trajectory(num_points=300):
    """生成示例轨迹数据"""
    t = np.linspace(0, 10, num_points)  # 10秒的轨迹
    x = 2 * np.sin(t)  # X轴正弦运动
    y = t  # Y轴线性运动
    z = np.zeros_like(t)  # Z轴保持恒定（假设地面高度为0）
    return np.column_stack((x, y, z))

class TrajectoryController(BaseController):
    """自定义控制器，用于沿着轨迹移动角色并控制动画"""
    
    def __init__(self, name, trajectory_data, character_prim_path):
        super().__init__(name)
        self.trajectory = trajectory_data
        self.character_prim_path = character_prim_path
        self.current_point_idx = 0
        self.last_position = None
        self.animation_speed = 0.0
        
    def forward(self, current_position):
        """计算下一步控制命令"""
        # 如果已经到达轨迹终点，停止移动
        if self.current_point_idx >= len(self.trajectory) - 1:
            return ArticulationAction(joint_positions=None, joint_velocities=None)
        
        # 获取当前目标点
        target_point = self.trajectory[self.current_point_idx]
        
        # 计算到目标点的距离
        distance = np.linalg.norm(target_point - current_position)
        
        # 如果足够接近当前目标点，移动到下一个点
        if distance < 0.1 and self.current_point_idx < len(self.trajectory) - 1:
            self.current_point_idx += 1
            target_point = self.trajectory[self.current_point_idx]
        
        # 计算移动方向
        direction = target_point - current_position
        direction_norm = direction / (np.linalg.norm(direction) + 1e-8)
        
        # 计算移动速度（基于与目标的距离）
        max_speed = 1.5  # 最大速度（米/秒）
        speed = min(max_speed, distance * 3.0)  # 简单的PD控制器
        
        # 计算动画速度（与移动速度成正比）
        self.animation_speed = speed * 2.0  # 调整系数以匹配动画
        
        # 计算朝向（假设角色需要面向移动方向）
        if self.last_position is not None:
            movement = current_position - self.last_position
            if np.linalg.norm(movement) > 0.01:
                forward_dir = movement / np.linalg.norm(movement)
                # 计算朝向角度（绕Z轴旋转）
                yaw = np.arctan2(forward_dir[1], forward_dir[0])
            else:
                yaw = 0
        else:
            yaw = 0
        
        self.last_position = current_position.copy()
        
        # 返回控制命令（这里需要根据实际角色关节进行调整）
        # 假设角色有速度控制和动画控制关节
        return ArticulationAction(
            joint_velocities=np.array([speed, yaw, self.animation_speed]),
            joint_positions=None
        )
    
    def reset(self):
        """重置控制器"""
        self.current_point_idx = 0
        self.last_position = None
        self.animation_speed = 0.0

def main():
    """主函数"""
    # 创建世界场景
    world = World()
    
    # 加载轨迹数据（这里使用示例数据，实际应从文件读取）
    trajectory_data = generate_sample_trajectory()
    
    # 添加角色到场景中
    # 注意：需要替换为实际的角色USD文件路径
    character_usd_path = "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/F_Businesss_02/F_Businesss_02.usd"
    add_reference_to_stage(usd_path=character_usd_path, prim_path="/World/Character")
    
    # 获取角色实例
    character = Articulation(prim_path="/World/Character", name="character")
    
    # 创建轨迹控制器
    trajectory_controller = TrajectoryController(
        name="trajectory_controller",
        trajectory_data=trajectory_data,
        character_prim_path="/World/Character"
    )
    
    # 设置世界场景
    world.scene.add(character)
    world.add_physics_callback("trajectory_callback", callback_fn=trajectory_controller.forward)
    
    # 初始化模拟
    world.reset()
    
    # 设置角色初始位置为轨迹起点
    character.set_world_pose(position=trajectory_data[0])
    
    # 运行模拟
    try:
        while simulation_app.is_running():
            world.step(render=True)
            
            # 检查是否到达轨迹终点
            if trajectory_controller.current_point_idx >= len(trajectory_data) - 1:
                carb.log_info("轨迹跟随完成")
                break
                
    except KeyboardInterrupt:
        carb.log_info("模拟被用户中断")
    
    finally:
        # 关闭模拟
        world.stop()
        simulation_app.close()

if __name__ == "__main__":
    main()