from isaacsim import SimulationApp
config = {
    "width": 1280,
    "height": 720,
    "headless": False,
    "experience": "./apps/isaacsim.exp.full.kit",
	 # "display_options": 3286,  # 更接近编辑器完整体验
    "extra_args": [
         "--enable", "omni.kit.language.simplified_chinese",
         "--/app/language=zh-CN"
    ],
}
simulation_app = SimulationApp(config)

import omni.usd
import carb
import omni.kit.app
import json
from pxr import UsdGeom, Gf, UsdShade, Sdf
import omni.usd
import hashlib

def parse_trajectories(json_path):
    with open(json_path, 'r') as f:
        data = json.load(f)

    parsed = {}
    for person, info in data.items():
        traj_list = info.get("trajectory", [])
        parsed[person] = []
        for point in traj_list:
            ts = point.get("timestamp")
            abs_ts = point.get("absolute_timestamp")
            pos = point.get("position")
            if ts is None or abs_ts is None or pos is None:
                print(f"Skipping incomplete point for {person}: {point}")
                continue
            parsed[person].append({
                "timestamp": ts,
                "absolute_timestamp": abs_ts,
                "position": pos
            })
    return parsed

def main():
    # json_file = "/data/1_LinuxFiles/Simulation/Yanglaoyuan_LLM/Scripts/test/trajectories.json"
    json_file = "/data/1_LinuxFiles/Simulation/trajectoriesDatas/trajectories.json"

    trajectories = parse_trajectories(json_file)
    stage = omni.usd.get_context().get_stage()
    
    # 预定义一些颜色
    base_colors = [
        (1, 0, 0),  # 红色
        (0, 1, 0),  # 绿色
        (0, 0, 1),  # 蓝色
        (1, 1, 0),  # 黄色
        (1, 0, 1),  # 紫色
        (0, 1, 1),  # 青色
        (1, 0.5, 0),  # 橙色
        (0.5, 0, 1),  # 紫罗兰色
        (0, 0.5, 1),  # 天蓝色
    ]
    
    for person_idx, (person, traj) in enumerate(trajectories.items()):
        print(f"== Trajectory for {person} ==")
        
        # 为每个person创建一个父级Xform
        parent_path = f"/World/{person}_trajectory"
        if not stage.GetPrimAtPath(parent_path):
            parent_xform = UsdGeom.Xform.Define(stage, parent_path)
        else:
            parent_xform = UsdGeom.Xform(stage.GetPrimAtPath(parent_path))
        
        # 为每个person生成一个唯一的颜色（基于person名称的哈希值）
        # 这样即使重新运行脚本，同一个person的颜色也会保持一致
        person_hash = int(hashlib.md5(person.encode()).hexdigest()[:8], 16)
        person_color = base_colors[person_hash % len(base_colors)]
        
        # 为这个person创建一个材质
        material_path = f"/World/Materials/{person}_material"
        material = UsdShade.Material.Define(stage, material_path)
        shader = UsdShade.Shader.Define(stage, material_path + "/Shader")
        shader.CreateIdAttr("UsdPreviewSurface")
        shader.CreateInput("diffuseColor", Sdf.ValueTypeNames.Color3f).Set(Gf.Vec3f(*person_color))
        material.CreateSurfaceOutput().ConnectToSource(shader.ConnectableAPI(), "surface")
        
        for idx, pt in enumerate(traj):
            # 为每个点创建一个唯一的Cube路径（放在父级Xform下）
            cube_path = f"{parent_path}/cube_{idx}"
            
            # 如果Cube已存在，先删除它（确保每次运行都是新的）
            if stage.GetPrimAtPath(cube_path):
                omni.usd.get_context().delete_prim(cube_path)
            
            # 创建新的Cube
            cube = UsdGeom.Cube.Define(stage, cube_path)
            
            # 设置Cube的大小
            cube.GetSizeAttr().Set(0.1)
            
            # 设置Cube的位置
            xform = UsdGeom.Xformable(cube.GetPrim())
            translate_op = xform.AddTranslateOp()
            # x,y 需要取反
            translate_op.Set(Gf.Vec3f(
                -pt['position'][0],
                -pt['position'][1],
                pt['position'][2]
            ))
            
            # 将材质应用到Cube
            UsdShade.MaterialBindingAPI(cube).Bind(material)
            
            print(
                f" Point {idx}: "
                f"timestamp = {pt['timestamp']}, "
                f"absolute_timestamp = {pt['absolute_timestamp']}, "
                f"position = {pt['position']}"
            )
        
        # 可选：为父级Xform添加一些描述性属性
        parent_xform.GetPrim().CreateAttribute("person_name", Sdf.ValueTypeNames.String).Set(person)
        parent_xform.GetPrim().CreateAttribute("trajectory_points", Sdf.ValueTypeNames.Int).Set(len(traj))
        
        print()
if __name__ == "__main__":
    main()

# stage = omni.usd.get_context().get_stage()

# # 创建 /World/Cube
# cube_path = "/World/Cube"
# if not stage.GetPrimAtPath(cube_path):
#     cube = UsdGeom.Cube.Define(stage, cube_path)
# else:
#     cube = UsdGeom.Cube(stage.GetPrimAtPath(cube_path))


# # 取 transform
# xform = UsdGeom.Xformable(cube.GetPrim())

# # 检查是否已有 translate op
# ops = xform.GetOrderedXformOps()
# translate_op = None
# for op in ops:
#     if op.GetOpName() == "xformOp:translate":
#         translate_op = op
#         break

# # 如果没有，就新建一个
# if translate_op is None:
#     translate_op = xform.AddTranslateOp()

# # 轨迹点
# waypoints = [
#     Gf.Vec3f(0.0, 0.0, 0.0),
#     Gf.Vec3f(100.0, 0.0, 0.0),
#     Gf.Vec3f(100.0, 100.0, 0.0),
# ]

# current_point = 0

# def on_update():
#     global current_point, translate_op
#     translate_op.Set(waypoints[current_point])
#     current_point = (current_point + 1) % len(waypoints)

# # # 注册 update 回调
# # app = omni.kit.app.get_app()
# # app.get_update_event_stream().create_subscription_to_pop(on_update)

# print("✅ Cube 已创建并在三个轨迹点循环移动（不会重复添加 translate op）")
# 保持主循环，直到窗口关闭
while simulation_app.is_running():
    simulation_app.update()
    # on_update()

simulation_app.close()