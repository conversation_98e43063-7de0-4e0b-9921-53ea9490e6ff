
from omni.isaac.kit import SimulationApp

simulation_app = SimulationApp({"headless": False})
from omni.kit.viewport.utility import get_active_viewport_and_window
import omni.ui.scene as sc
import omni.ui as ui

viewport, window = get_active_viewport_and_window()
scene_view = sc.SceneView()

with window.get_frame("overlay_frame"):
    with scene_view.scene:
        sc.Label("你好新UI• FPS: 0", position=(10, 10))  # 示例 Label

viewport.add_scene_view(scene_view)

# 在每次 update 中更新标签显示的 FPS 数值即可
		# 保持主循环，直到窗口关闭
while simulation_app.is_running():
    simulation_app.update()

simulation_app.close()

style_system = {
            "Button": {
                "background_color": cl("#E1E1E1"),
                "border_color": cl("#ADADAD"),
                "border_width": 0.5,
                "border_radius": 0.0,
                "margin": 5.0,
                "padding": 5.0,
            },
            "Button.Label": {
                "color": cl.black,
            },
            "Button:hovered": {
                "background_color": cl("#E5F1FB"),
                "border_color": cl("#0078D7")
            },
            "Button:pressed": {
                "background_color": cl("#CCE4F7"),
                "border_color": cl("#005499"),
                "border_width": 1.0
            },
        }

        with ui.HStack(style=style_system):
            ui.Button("One")
            ui.Button("Two", style={"color": cl("#AAAAAA")})
            ui.Button("Three", style={
                "background_color": cl("#097EFF"),
                "background_gradient_color": cl("#6DB2FA")}
            )
            ui.Button(
                "Four", style={
                    ":hovered": {
                        "background_color": cl("#006EFF"),
                        "background_gradient_color": cl("#5AAEFF")
                    }
                }
            )
            ui.Button(
                "Five",
                style={
                    "Button:pressed": {
                        "background_color": cl("#6DB2FA"),
                        "background_gradient_color": cl("#097EFF")
                    }
                },
            )