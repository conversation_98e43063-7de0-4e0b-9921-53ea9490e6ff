import os
import subprocess

def load_bat_env(bat_path):
    """加载 bat 环境变量到 Python os.environ"""
    # /V:ON 启用延迟变量展开，避免 %% 转义问题
    process = subprocess.Popen(
        f'cmd /V:ON /c "{bat_path} --no-ros-env && set"',
        stdout=subprocess.PIPE,
        shell=True
    )
    for line in process.stdout:
        line = line.decode(errors="ignore").strip()
        if "=" in line:
            key, value = line.split("=", 1)
            os.environ[key] = value

# 1. 先加载 bat 环境（不启动 Kit）
load_bat_env(r"D:\Work\Simulation\IsaacSim\isaac-sim.bat")

from omni.isaac.kit import SimulationApp

simulation_app = SimulationApp({"headless": False})
import omni.ui as ui
# 1. 创建窗口对象
window = ui.Window("HUD Toolbar", width=200, height=30, visible=True, dock_preference=ui.DockPreference.RIGHT_TOP)

# 2. 使用 window.frame 作为上下文来添加控件
with window.frame:
    with ui.HStack():
        def on_click():
            print("Custom HUD Button clicked!")
        ui.Button(text="My HUD Button", clicked_fn=on_click)

while simulation_app.is_running():
    simulation_app.update()

simulation_app.close()
