
from isaacsim import SimulationApp

config = {
    "headless": False,
    "experience": r".\apps\isaacsim.exp.full.kit",
    "extra_args": [
         "--enable", "omni.kit.language.simplified_chinese",
         "--/app/language=zh-CN",
        # "--/app/settings/persistent=1",
        # '--/app/font/file=${fonts}/OpenSans-SemiBold.ttf',  # 你已在 base.kit 改字体，这里显式同步一下
    ],
}
app = SimulationApp(config)

# # 再次兜底确保扩展已启用（不同版本返回结构不同，直接尝试启用即可）
# import omni.kit.app as kitapp
# em = kitapp.get_app().get_extension_manager()
# try:
    # if not em.is_extension_enabled("omni.kit.language.simplified_chinese"):
        # em.set_extension_enabled("omni.kit.language.simplified_chinese", True)
# except Exception:
    # pass
# -------------------- 测试中文显示 --------------------
import omni.ui as ui

viewport_window = ui.Workspace.get_window("Viewport")

with viewport_window.frame:
    with ui.ZStack():
        # 底层：3D 内容（默认就在底层）
        # 顶层：UI 控件
        with ui.Frame(width=300, height=200, style={"background_color": 0x88000000}):
            with ui.VStack():
                ui.Label("UI 控件")
                ui.Button("按钮", clicked_fn=lambda: print("点击"))
                ui.StringField(placeholder="输入框")
while app.is_running():
    app.update()
app.close()