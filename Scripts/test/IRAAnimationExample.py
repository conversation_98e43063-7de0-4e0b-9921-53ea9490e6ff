"""
使用Isaac Replicator Animation (IRA) 的人物动画示例
展示如何使用IRA API控制人物动画状态
"""

from isaacsim import SimulationApp
config = {
    "width": 1280,
    "height": 720,
    "headless": False,
}
simulation_app = SimulationApp(config)

import omni.usd
import omni.kit.app
import carb
import omni.ui as ui
import omni.replicator.core as rep
from pxr import UsdGeom, Gf, UsdSkel, Sdf, UsdShade
import asyncio
import math

# 启用必要的扩展
extension_manager = omni.kit.app.get_app().get_extension_manager()
extension_manager.set_extension_enabled_immediate("omni.anim.people", True)
extension_manager.set_extension_enabled_immediate("omni.anim.graph", True)

# 导入动画相关模块
try:
    import omni.anim.people.core as people_core
    import omni.anim.people.scripts as people_scripts
    print("成功导入omni.anim.people模块")
except ImportError as e:
    print(f"导入omni.anim.people失败: {e}")
    people_core = None
    people_scripts = None

# 全局变量
character_path = "/World/Character"
current_animation = "idle"
animation_speed = 1.0

class IRAAnimationController:
    """使用<PERSON> Sim 5.0动画API的控制器"""

    def __init__(self, character_prim_path):
        self.character_path = character_prim_path
        self.stage = omni.usd.get_context().get_stage()
        self.character_prim = None
        self.animation_state = "idle"
        self.available_animations = ["idle", "walk", "run"]
        self.people_manager = None
        
    def create_character_with_animation(self):
        """创建带动画的角色"""
        try:
            # 尝试加载人物模型
            character_urls = [
                "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/F_Businesss_02/F_Businesss_02.usd",
                "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/M_Businesss_01/M_Businesss_01.usd"
            ]

            for url in character_urls:
                try:
                    print(f"加载角色: {url}")

                    # 创建角色prim
                    self.character_prim = self.stage.DefinePrim(self.character_path, "Xform")
                    self.character_prim.GetReferences().AddReference(url)

                    # 设置初始位置
                    xform = UsdGeom.Xformable(self.character_prim)
                    xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, 0))

                    print(f"角色创建成功: {self.character_path}")

                    # 初始化动画系统
                    self.setup_animation_system()

                    return self.character_prim

                except Exception as e:
                    print(f"加载失败: {e}")
                    continue

            # 如果加载失败，使用备用方法
            return self.create_fallback_character()

        except Exception as e:
            print(f"创建角色失败: {e}")
            return self.create_fallback_character()

    def setup_animation_system(self):
        """设置动画系统"""
        try:
            if people_core and people_scripts:
                # 使用omni.anim.people设置动画
                print("使用omni.anim.people设置动画系统")

                # 初始化人物管理器
                if hasattr(people_core, 'PeopleManager'):
                    self.people_manager = people_core.PeopleManager()

                # 设置角色为可动画状态
                self.setup_character_for_animation()

            else:
                print("omni.anim.people不可用，使用基础动画系统")
                self.setup_basic_animation()

        except Exception as e:
            print(f"设置动画系统失败: {e}")
            self.setup_basic_animation()

    def setup_character_for_animation(self):
        """为角色设置动画"""
        try:
            # 查找SkelRoot
            skel_root = self.find_skel_root(self.character_prim)
            if skel_root:
                print(f"找到SkelRoot: {skel_root.GetPath()}")

                # 创建动画状态属性
                if not skel_root.HasAttribute("animationState"):
                    skel_root.CreateAttribute("animationState", Sdf.ValueTypeNames.String)
                skel_root.GetAttribute("animationState").Set("idle")

                # 创建动画速度属性
                if not skel_root.HasAttribute("animationSpeed"):
                    skel_root.CreateAttribute("animationSpeed", Sdf.ValueTypeNames.Float)
                skel_root.GetAttribute("animationSpeed").Set(1.0)

                print("角色动画设置完成")
            else:
                print("未找到SkelRoot，使用基础动画")
                self.setup_basic_animation()

        except Exception as e:
            print(f"设置角色动画失败: {e}")
            self.setup_basic_animation()

    def setup_basic_animation(self):
        """设置基础动画系统"""
        try:
            # 在角色prim上创建动画状态属性
            if not self.character_prim.HasAttribute("animationState"):
                self.character_prim.CreateAttribute("animationState", Sdf.ValueTypeNames.String)
            self.character_prim.GetAttribute("animationState").Set("idle")

            if not self.character_prim.HasAttribute("animationSpeed"):
                self.character_prim.CreateAttribute("animationSpeed", Sdf.ValueTypeNames.Float)
            self.character_prim.GetAttribute("animationSpeed").Set(1.0)

            print("基础动画系统设置完成")

        except Exception as e:
            print(f"设置基础动画失败: {e}")
    
    def create_fallback_character(self):
        """备用角色创建方法"""
        print("使用备用方法创建角色")
        
        # 创建简单胶囊体
        capsule = UsdGeom.Capsule.Define(self.stage, self.character_path)
        capsule.GetHeightAttr().Set(1.8)
        capsule.GetRadiusAttr().Set(0.3)
        
        # 设置位置
        xform = UsdGeom.Xformable(capsule.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, 0.9))
        
        # 添加动画状态属性
        capsule.GetPrim().CreateAttribute("animationState", Sdf.ValueTypeNames.String)
        capsule.GetPrim().GetAttribute("animationState").Set("idle")
        
        self.character_prim = capsule.GetPrim()
        return self.character_prim
    
    def set_animation_state(self, animation_name, speed=1.0):
        """设置动画状态"""
        try:
            if animation_name not in self.available_animations:
                print(f"不支持的动画: {animation_name}")
                return False

            self.animation_state = animation_name
            global current_animation, animation_speed
            current_animation = animation_name
            animation_speed = speed

            # 使用omni.anim.people设置动画
            if self.people_manager and people_scripts:
                try:
                    # 尝试使用people脚本设置动画
                    self.set_people_animation(animation_name, speed)
                except Exception as e:
                    print(f"使用people动画失败: {e}")
                    self.set_basic_animation(animation_name, speed)
            else:
                self.set_basic_animation(animation_name, speed)

            print(f"动画状态设置为: {animation_name}, 速度: {speed}")
            return True

        except Exception as e:
            print(f"设置动画状态失败: {e}")
            return False

    def set_people_animation(self, animation_name, speed):
        """使用omni.anim.people设置动画"""
        try:
            # 查找SkelRoot
            skel_root = self.find_skel_root(self.character_prim)
            if skel_root:
                # 设置动画状态属性
                if skel_root.HasAttribute("animationState"):
                    skel_root.GetAttribute("animationState").Set(animation_name)
                if skel_root.HasAttribute("animationSpeed"):
                    skel_root.GetAttribute("animationSpeed").Set(speed)

                # 如果有people管理器，使用它来控制动画
                if hasattr(self.people_manager, 'set_character_animation'):
                    self.people_manager.set_character_animation(str(skel_root.GetPath()), animation_name, speed)

                print(f"使用people系统设置动画: {animation_name}")

        except Exception as e:
            print(f"people动画设置失败: {e}")
            raise e

    def set_basic_animation(self, animation_name, speed):
        """设置基础动画"""
        try:
            # 查找骨骼动画组件
            skel_root = self.find_skel_root(self.character_prim)
            if skel_root:
                # 设置骨骼动画
                self.apply_skeletal_animation(skel_root, animation_name, speed)
            else:
                # 如果没有骨骼，使用属性标记
                if self.character_prim.HasAttribute("animationState"):
                    self.character_prim.GetAttribute("animationState").Set(animation_name)
                    self.character_prim.GetAttribute("animationSpeed").Set(speed)
                else:
                    self.character_prim.CreateAttribute("animationState", Sdf.ValueTypeNames.String)
                    self.character_prim.GetAttribute("animationState").Set(animation_name)
                    self.character_prim.CreateAttribute("animationSpeed", Sdf.ValueTypeNames.Float)
                    self.character_prim.GetAttribute("animationSpeed").Set(speed)

            print(f"使用基础系统设置动画: {animation_name}")

        except Exception as e:
            print(f"基础动画设置失败: {e}")
            raise e
    
    def find_skel_root(self, prim):
        """查找骨骼根节点"""
        if prim.GetTypeName() == "SkelRoot":
            return prim
        
        for child in prim.GetChildren():
            result = self.find_skel_root(child)
            if result:
                return result
        
        return None
    
    def apply_skeletal_animation(self, skel_root, animation_name, speed):
        """应用骨骼动画"""
        try:
            # 查找SkelAnimation
            for child in skel_root.GetChildren():
                if child.GetTypeName() == "SkelAnimation":
                    # 设置动画属性
                    if child.HasAttribute("animationSource"):
                        child.GetAttribute("animationSource").Set(animation_name)
                    else:
                        child.CreateAttribute("animationSource", Sdf.ValueTypeNames.String)
                        child.GetAttribute("animationSource").Set(animation_name)
                    
                    if child.HasAttribute("playbackRate"):
                        child.GetAttribute("playbackRate").Set(speed)
                    else:
                        child.CreateAttribute("playbackRate", Sdf.ValueTypeNames.Float)
                        child.GetAttribute("playbackRate").Set(speed)
                    
                    print(f"骨骼动画设置成功: {animation_name}")
                    return True
            
            print("未找到SkelAnimation组件")
            return False
            
        except Exception as e:
            print(f"应用骨骼动画失败: {e}")
            return False
    
    def get_available_animations(self):
        """获取可用动画列表"""
        return self.available_animations.copy()

def create_ira_ui():
    """创建IRA控制UI"""
    global ira_controller
    
    window = ui.Window("IRA动画控制", width=350, height=400)
    
    def set_idle():
        ira_controller.set_animation_state("idle", 1.0)
    
    def set_walk():
        ira_controller.set_animation_state("walk", 1.0)
    
    def set_run():
        ira_controller.set_animation_state("run", 1.5)
    
    def set_custom_speed(speed):
        ira_controller.set_animation_state(current_animation, speed)
    
    with window.frame:
        with ui.VStack(spacing=10):
            ui.Label("IRA动画控制面板", style={"font_size": 18, "color": 0xFF00AAFF})
            
            # 动画选择
            ui.Label("动画状态选择:", style={"font_size": 14})
            with ui.VStack():
                ui.Button("Idle (静止)", clicked_fn=set_idle, width=200)
                ui.Button("Walk (行走)", clicked_fn=set_walk, width=200)
                ui.Button("Run (跑步)", clicked_fn=set_run, width=200)
            
            ui.Separator()
            
            # 速度控制
            ui.Label("动画播放速度:", style={"font_size": 14})
            speed_slider = ui.FloatSlider(min=0.1, max=3.0, step=0.1, width=300)
            speed_slider.model.set_value(1.0)
            speed_slider.model.add_value_changed_fn(
                lambda m: set_custom_speed(m.get_value_as_float())
            )
            
            ui.Separator()
            
            # 状态显示
            ui.Label("当前状态:", style={"font_size": 14})
            status_label = ui.Label("", style={"color": 0xFF00FF00})
            
            # 动画系统信息
            ui.Separator()
            ui.Label("动画系统:", style={"font_size": 14})
            ui.Label("• 使用Isaac Sim 5.0动画API")
            ui.Label("• 支持omni.anim.people扩展")
            ui.Label("• 实时动画速度调节")
            ui.Label("• 智能动画状态管理")

            # 控制说明
            ui.Separator()
            ui.Label("控制说明:", style={"font_size": 12})
            ui.Label("• 点击按钮切换动画状态")
            ui.Label("• 使用滑块调节播放速度")
            ui.Label("• 键盘1-3键快速切换动画")
    
    return window

def setup_keyboard_controls():
    """设置键盘控制"""
    global ira_controller

    def on_keyboard_event(event):
        if event.type == carb.input.KeyboardEventType.KEY_PRESS:
            if event.input == carb.input.KeyboardInput.KEY_1:
                ira_controller.set_animation_state("idle", 1.0)
            elif event.input == carb.input.KeyboardInput.KEY_2:
                ira_controller.set_animation_state("walk", 1.0)
            elif event.input == carb.input.KeyboardInput.KEY_3:
                ira_controller.set_animation_state("run", 1.5)
            elif event.input == carb.input.KeyboardInput.SPACE:
                # 空格键在idle和walk之间切换
                if current_animation == "idle":
                    ira_controller.set_animation_state("walk", 1.0)
                else:
                    ira_controller.set_animation_state("idle", 1.0)
        return True

    try:
        # 使用carb输入系统
        input_interface = carb.input.acquire_input_interface()
        keyboard = input_interface.acquire_keyboard_capture()

        def keyboard_handler(event):
            return on_keyboard_event(event)

        input_interface.subscribe_to_keyboard_events(keyboard, keyboard_handler)
        print("键盘控制已设置")
    except Exception as e:
        print(f"设置键盘控制失败: {e}")

def create_scene_environment():
    """创建场景环境"""
    stage = omni.usd.get_context().get_stage()
    
    # 创建地面
    ground_path = "/World/Ground"
    if not stage.GetPrimAtPath(ground_path):
        ground = UsdGeom.Cube.Define(stage, ground_path)
        ground.GetSizeAttr().Set(15.0)
        
        xform = UsdGeom.Xformable(ground.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, -7.5))
        xform.AddScaleOp().Set(Gf.Vec3f(1, 1, 0.1))
    
    # 添加光源
    light_path = "/World/Light"
    if not stage.GetPrimAtPath(light_path):
        light = UsdGeom.DistantLight.Define(stage, light_path)
        xform = UsdGeom.Xformable(light.GetPrim())
        xform.AddRotateXYZOp().Set(Gf.Vec3f(-45, 0, 0))

# 全局控制器实例
ira_controller = None

def main():
    """主函数"""
    global ira_controller
    
    print("=== IRA动画控制示例 ===")
    print("使用Isaac Replicator Animation控制人物动画")
    print()
    
    # 创建场景环境
    create_scene_environment()
    
    # 创建动画控制器
    ira_controller = IRAAnimationController(character_path)

    # 创建角色
    character_prim = ira_controller.create_character_with_animation()
    if not character_prim:
        print("创建角色失败")
        return

    # 创建UI
    ui_window = create_ira_ui()

    # 设置键盘控制
    setup_keyboard_controls()
    
    print("\n=== 动画控制说明 ===")
    print("UI控制:")
    print("• 使用按钮切换不同动画状态")
    print("• 使用滑块调节动画播放速度")
    print("键盘控制:")
    print("• 1键: Idle动画")
    print("• 2键: Walk动画")
    print("• 3键: Run动画")
    print("• 空格键: 在Idle和Walk之间切换")
    print("===================")

    # 设置初始动画状态
    ira_controller.set_animation_state("idle", 1.0)

if __name__ == "__main__":
    main()
    
    # 主循环
    while simulation_app.is_running():
        simulation_app.update()

simulation_app.close()
