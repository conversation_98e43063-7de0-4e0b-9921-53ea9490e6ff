"""
使用Isaac Replicator Animation (IRA) 的人物动画示例
展示如何使用IRA API控制人物动画状态
"""

from isaacsim import SimulationApp
config = {
    "width": 1280,
    "height": 720,
    "headless": False,
}
simulation_app = SimulationApp(config)

import omni.usd
import omni.kit.app
import carb
import omni.ui as ui
import omni.replicator.core as rep
from pxr import UsdGeom, Gf, UsdSkel, Sdf
import asyncio
import math

# 全局变量
character_path = "/World/Character"
current_animation = "idle"
animation_speed = 1.0

class IRAAnimationController:
    """使用IRA的动画控制器"""
    
    def __init__(self, character_prim_path):
        self.character_path = character_prim_path
        self.stage = omni.usd.get_context().get_stage()
        self.character_prim = None
        self.animation_state = "idle"
        self.available_animations = ["idle", "walk", "run", "wave"]
        
    def create_character_with_ira(self):
        """使用IRA创建带动画的角色"""
        try:
            # 使用IRA创建人物
            with rep.new_layer():
                # 创建人物实例
                character_urls = [
                    "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/F_Businesss_02/F_Businesss_02.usd",
                    "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/M_Businesss_01/M_Businesss_01.usd"
                ]
                
                for url in character_urls:
                    try:
                        print(f"使用IRA加载角色: {url}")
                        
                        # 使用Replicator创建角色
                        character = rep.create.from_usd(url, semantics=[("class", "person")])
                        
                        # 设置位置
                        with character:
                            rep.modify.pose(
                                position=(0, 0, 0),
                                rotation=(0, 0, 0)
                            )
                        
                        # 获取角色prim
                        self.character_prim = self.stage.GetPrimAtPath(self.character_path)
                        if not self.character_prim:
                            # 如果路径不匹配，查找创建的prim
                            for prim in self.stage.Traverse():
                                if prim.GetReferences().GetAddedOrExplicitItems():
                                    for ref in prim.GetReferences().GetAddedOrExplicitItems():
                                        if url in str(ref.assetPath):
                                            self.character_prim = prim
                                            self.character_path = str(prim.GetPath())
                                            break
                                    if self.character_prim:
                                        break
                        
                        if self.character_prim:
                            print(f"IRA角色创建成功: {self.character_path}")
                            return self.character_prim
                        
                    except Exception as e:
                        print(f"IRA加载失败: {e}")
                        continue
            
            # 如果IRA失败，使用传统方法
            return self.create_fallback_character()
            
        except Exception as e:
            print(f"IRA创建角色失败: {e}")
            return self.create_fallback_character()
    
    def create_fallback_character(self):
        """备用角色创建方法"""
        print("使用备用方法创建角色")
        
        # 创建简单胶囊体
        capsule = UsdGeom.Capsule.Define(self.stage, self.character_path)
        capsule.GetHeightAttr().Set(1.8)
        capsule.GetRadiusAttr().Set(0.3)
        
        # 设置位置
        xform = UsdGeom.Xformable(capsule.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, 0.9))
        
        # 添加动画状态属性
        capsule.GetPrim().CreateAttribute("animationState", Sdf.ValueTypeNames.String)
        capsule.GetPrim().GetAttribute("animationState").Set("idle")
        
        self.character_prim = capsule.GetPrim()
        return self.character_prim
    
    def set_animation_state(self, animation_name, speed=1.0):
        """设置动画状态"""
        try:
            if animation_name not in self.available_animations:
                print(f"不支持的动画: {animation_name}")
                return False
            
            self.animation_state = animation_name
            global current_animation, animation_speed
            current_animation = animation_name
            animation_speed = speed
            
            # 查找骨骼动画组件
            skel_root = self.find_skel_root(self.character_prim)
            if skel_root:
                # 设置骨骼动画
                self.apply_skeletal_animation(skel_root, animation_name, speed)
            else:
                # 如果没有骨骼，使用属性标记
                if self.character_prim.HasAttribute("animationState"):
                    self.character_prim.GetAttribute("animationState").Set(animation_name)
                else:
                    self.character_prim.CreateAttribute("animationState", Sdf.ValueTypeNames.String)
                    self.character_prim.GetAttribute("animationState").Set(animation_name)
            
            print(f"动画状态设置为: {animation_name}, 速度: {speed}")
            return True
            
        except Exception as e:
            print(f"设置动画状态失败: {e}")
            return False
    
    def find_skel_root(self, prim):
        """查找骨骼根节点"""
        if prim.GetTypeName() == "SkelRoot":
            return prim
        
        for child in prim.GetChildren():
            result = self.find_skel_root(child)
            if result:
                return result
        
        return None
    
    def apply_skeletal_animation(self, skel_root, animation_name, speed):
        """应用骨骼动画"""
        try:
            # 查找SkelAnimation
            for child in skel_root.GetChildren():
                if child.GetTypeName() == "SkelAnimation":
                    # 设置动画属性
                    if child.HasAttribute("animationSource"):
                        child.GetAttribute("animationSource").Set(animation_name)
                    else:
                        child.CreateAttribute("animationSource", Sdf.ValueTypeNames.String)
                        child.GetAttribute("animationSource").Set(animation_name)
                    
                    if child.HasAttribute("playbackRate"):
                        child.GetAttribute("playbackRate").Set(speed)
                    else:
                        child.CreateAttribute("playbackRate", Sdf.ValueTypeNames.Float)
                        child.GetAttribute("playbackRate").Set(speed)
                    
                    print(f"骨骼动画设置成功: {animation_name}")
                    return True
            
            print("未找到SkelAnimation组件")
            return False
            
        except Exception as e:
            print(f"应用骨骼动画失败: {e}")
            return False
    
    def get_available_animations(self):
        """获取可用动画列表"""
        return self.available_animations.copy()

def create_ira_ui():
    """创建IRA控制UI"""
    global ira_controller
    
    window = ui.Window("IRA动画控制", width=350, height=400)
    
    def set_idle():
        ira_controller.set_animation_state("idle", 1.0)
    
    def set_walk():
        ira_controller.set_animation_state("walk", 1.0)
    
    def set_run():
        ira_controller.set_animation_state("run", 1.5)
    
    def set_wave():
        ira_controller.set_animation_state("wave", 1.0)
    
    def set_custom_speed(speed):
        ira_controller.set_animation_state(current_animation, speed)
    
    with window.frame:
        with ui.VStack(spacing=10):
            ui.Label("IRA动画控制面板", style={"font_size": 18, "color": 0xFF00AAFF})
            
            # 动画选择
            ui.Label("动画状态选择:", style={"font_size": 14})
            with ui.VStack():
                ui.Button("Idle (静止)", clicked_fn=set_idle, width=200)
                ui.Button("Walk (行走)", clicked_fn=set_walk, width=200)
                ui.Button("Run (跑步)", clicked_fn=set_run, width=200)
                ui.Button("Wave (挥手)", clicked_fn=set_wave, width=200)
            
            ui.Separator()
            
            # 速度控制
            ui.Label("动画播放速度:", style={"font_size": 14})
            speed_slider = ui.FloatSlider(min=0.1, max=3.0, step=0.1, width=300)
            speed_slider.model.set_value(1.0)
            speed_slider.model.add_value_changed_fn(
                lambda m: set_custom_speed(m.get_value_as_float())
            )
            
            ui.Separator()
            
            # 状态显示
            ui.Label("当前状态:", style={"font_size": 14})
            status_label = ui.Label("", style={"color": 0xFF00FF00})
            
            # IRA信息
            ui.Separator()
            ui.Label("IRA功能:", style={"font_size": 14})
            ui.Label("• 使用Isaac Replicator Animation")
            ui.Label("• 支持多种动画状态")
            ui.Label("• 实时动画速度调节")
            ui.Label("• 智能动画状态管理")
            
            # 控制说明
            ui.Separator()
            ui.Label("控制说明:", style={"font_size": 12})
            ui.Label("• 点击按钮切换动画状态")
            ui.Label("• 使用滑块调节播放速度")
            ui.Label("• 键盘1-4键快速切换动画")
    
    return window

def setup_ira_keyboard():
    """设置IRA键盘控制"""
    global ira_controller
    
    def on_keyboard_event(event):
        if event.type == carb.input.KeyboardEventType.KEY_PRESS:
            if event.input == carb.input.KeyboardInput.KEY_1:
                ira_controller.set_animation_state("idle", 1.0)
            elif event.input == carb.input.KeyboardInput.KEY_2:
                ira_controller.set_animation_state("walk", 1.0)
            elif event.input == carb.input.KeyboardInput.KEY_3:
                ira_controller.set_animation_state("run", 1.5)
            elif event.input == carb.input.KeyboardInput.KEY_4:
                ira_controller.set_animation_state("wave", 1.0)
            elif event.input == carb.input.KeyboardInput.SPACE:
                # 空格键在idle和walk之间切换
                if current_animation == "idle":
                    ira_controller.set_animation_state("walk", 1.0)
                else:
                    ira_controller.set_animation_state("idle", 1.0)
        return True
    
    try:
        app_window = omni.kit.app.get_app().get_window()
        keyboard = app_window.get_keyboard()
        keyboard.subscribe_to_keyboard_events(on_keyboard_event)
        print("IRA键盘控制已设置")
    except Exception as e:
        print(f"设置IRA键盘控制失败: {e}")

def create_scene_environment():
    """创建场景环境"""
    stage = omni.usd.get_context().get_stage()
    
    # 创建地面
    ground_path = "/World/Ground"
    if not stage.GetPrimAtPath(ground_path):
        ground = UsdGeom.Cube.Define(stage, ground_path)
        ground.GetSizeAttr().Set(15.0)
        
        xform = UsdGeom.Xformable(ground.GetPrim())
        xform.AddTranslateOp().Set(Gf.Vec3f(0, 0, -7.5))
        xform.AddScaleOp().Set(Gf.Vec3f(1, 1, 0.1))
    
    # 添加光源
    light_path = "/World/Light"
    if not stage.GetPrimAtPath(light_path):
        light = UsdGeom.DistantLight.Define(stage, light_path)
        xform = UsdGeom.Xformable(light.GetPrim())
        xform.AddRotateXYZOp().Set(Gf.Vec3f(-45, 0, 0))

# 全局控制器实例
ira_controller = None

def main():
    """主函数"""
    global ira_controller
    
    print("=== IRA动画控制示例 ===")
    print("使用Isaac Replicator Animation控制人物动画")
    print()
    
    # 创建场景环境
    create_scene_environment()
    
    # 创建IRA控制器
    ira_controller = IRAAnimationController(character_path)
    
    # 创建角色
    character_prim = ira_controller.create_character_with_ira()
    if not character_prim:
        print("创建角色失败")
        return
    
    # 创建UI
    ui_window = create_ira_ui()
    
    # 设置键盘控制
    setup_ira_keyboard()
    
    print("\n=== IRA控制说明 ===")
    print("UI控制:")
    print("• 使用按钮切换不同动画状态")
    print("• 使用滑块调节动画播放速度")
    print("键盘控制:")
    print("• 1键: Idle动画")
    print("• 2键: Walk动画")
    print("• 3键: Run动画")
    print("• 4键: Wave动画")
    print("• 空格键: 在Idle和Walk之间切换")
    print("===================")
    
    # 设置初始动画状态
    ira_controller.set_animation_state("idle", 1.0)

if __name__ == "__main__":
    main()
    
    # 主循环
    while simulation_app.is_running():
        simulation_app.update()

simulation_app.close()
