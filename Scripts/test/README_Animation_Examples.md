# Isaac Sim 人物动画控制示例

## 概述

本文档介绍了三个使用Isaac Sim进行人物动画控制的Python脚本示例，展示了如何使用IRA (Isaac Replicator Animation)和AnimationGraph来控制人物的行走和Idle动画。

## 脚本文件说明

### 1. CharacterAnimationController.py (完整功能版本)
**功能特点:**
- 完整的人物动画控制系统
- 支持移动控制和动画自动切换
- 包含UI控制面板和键盘快捷键
- 智能动画混合和平滑过渡
- 支持多种移动模式（直线移动、圆形路径等）

**主要类:**
- `CharacterAnimationController`: 核心动画控制器类
- 支持自动根据移动速度切换动画状态
- 包含位置更新和动画状态管理

### 2. SimpleAnimationExample.py (基础示例)
**功能特点:**
- 简化的动画控制示例
- 专注于AnimationGraph的基本用法
- 使用OmniGraph创建动画混合节点
- 支持权重滑块精确控制动画混合

**核心功能:**
- 创建Idle和Walk动画剪辑
- 使用BlendNode进行动画混合
- 实时权重调节

### 3. IRAAnimationExample.py (IRA专业版本)
**功能特点:**
- 使用Isaac Replicator Animation (IRA)
- 支持多种动画状态（Idle、Walk、Run、Wave）
- 专业的动画状态管理
- 骨骼动画系统集成

**IRA特性:**
- 使用`omni.replicator.core`创建角色
- 支持语义标签和高级动画控制
- 更好的动画资源管理

## 技术实现对比

### AnimationGraph 方法
```python
# 使用OmniGraph创建动画图
og.Controller.edit(
    {"graph_path": animation_graph_path, "evaluator_name": "execution"},
    {
        og.Controller.Keys.CREATE_NODES: [
            ("IdleClip", "omni.anim.clip"),
            ("WalkClip", "omni.anim.clip"),
            ("BlendTwoClips", "omni.anim.blend"),
        ],
        og.Controller.Keys.CONNECT: [
            ("IdleClip.outputs:animation", "BlendTwoClips.inputs:clipA"),
            ("WalkClip.outputs:animation", "BlendTwoClips.inputs:clipB"),
        ],
    },
)
```

### IRA 方法
```python
# 使用IRA创建角色
with rep.new_layer():
    character = rep.create.from_usd(url, semantics=[("class", "person")])
    with character:
        rep.modify.pose(position=(0, 0, 0), rotation=(0, 0, 0))
```

## 使用方法

### 运行脚本
```bash
# 运行完整功能版本
python Scripts/test/CharacterAnimationController.py

# 运行基础示例
python Scripts/test/SimpleAnimationExample.py

# 运行IRA示例
python Scripts/test/IRAAnimationExample.py
```

### 控制方式

#### UI控制
- 所有脚本都提供图形化控制面板
- 支持按钮快速切换动画状态
- 滑块精确调节动画参数

#### 键盘控制
**通用快捷键:**
- `空格键`: 切换Idle/Walking状态
- `1-4键`: 快速切换不同动画状态

**移动控制 (CharacterAnimationController.py):**
- `W`: 向前移动
- `S`: 向后移动
- `A`: 向左移动
- `D`: 向右移动

## 动画状态说明

### 基本动画状态
1. **Idle**: 静止状态，角色保持站立姿势
2. **Walking**: 行走状态，正常步行动画
3. **Running**: 跑步状态（IRA示例中可用）
4. **Wave**: 挥手动画（IRA示例中可用）

### 动画混合
- 支持0.0-1.0之间的权重混合
- 0.0 = 纯Idle动画
- 1.0 = 纯Walking动画
- 中间值 = 混合动画效果

## 技术要点

### 1. 角色模型加载
```python
# 优先尝试加载人物USD模型
character_urls = [
    "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/F_Businesss_02/F_Businesss_02.usd",
    "/data/1_LinuxFiles/Simulation/UsdFiles/F_Medical_01/F_Medical_01.usd"
]

# 备用方案：创建简单几何体
capsule = UsdGeom.Capsule.Define(stage, character_path)
```

### 2. 动画图创建
- 使用`omni.graph.core`创建动画节点
- 连接动画剪辑到混合节点
- 设置骨骼动画输出

### 3. 状态管理
- 实时监控动画状态
- 平滑的状态过渡
- 错误处理和备用方案

## 扩展功能

### 可添加的功能
1. **更多动画状态**: 跳跃、蹲下、转身等
2. **动画序列**: 复杂的动画组合
3. **表情控制**: 面部动画
4. **服装变换**: 动态更换角色外观
5. **群体动画**: 多角色协调动画

### 自定义动画
```python
# 添加自定义动画剪辑
def add_custom_animation(clip_name, clip_path):
    og.Controller.edit(
        {"graph_path": graph_path},
        {
            og.Controller.Keys.CREATE_NODES: [
                (f"{clip_name}Clip", "omni.anim.clip"),
            ],
            og.Controller.Keys.SET_VALUES: [
                (f"{clip_name}Clip.inputs:clipPath", clip_path),
            ],
        },
    )
```

## 故障排除

### 常见问题
1. **角色不显示**: 检查USD文件路径和网络连接
2. **动画不播放**: 确认动画图创建成功
3. **UI无响应**: 检查UI事件绑定
4. **键盘控制失效**: 验证键盘事件注册

### 调试技巧
- 查看控制台输出信息
- 检查USD场景图结构
- 验证动画节点连接
- 测试备用角色模型

## 性能优化

### 建议
1. 限制同时活跃的动画数量
2. 使用LOD系统管理远距离角色
3. 优化动画更新频率
4. 合理使用动画缓存

## 总结

这三个示例展示了Isaac Sim中人物动画控制的不同方法和复杂度级别：

- **SimpleAnimationExample.py**: 适合学习基础概念
- **CharacterAnimationController.py**: 适合完整应用开发
- **IRAAnimationExample.py**: 适合专业动画制作

选择合适的示例作为起点，根据项目需求进行扩展和定制。
