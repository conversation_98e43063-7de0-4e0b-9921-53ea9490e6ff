# Isaac Sim 5.0 人物动画控制示例

## 概述

本文档介绍了针对Isaac Sim 5.0版本的人物动画控制Python脚本示例，展示了如何使用正确的Isaac Sim 5.0 API来控制人物的行走和Idle动画。这些脚本已经更新以兼容Isaac Sim 5.0的动画系统。

## 脚本文件说明

### 1. PureActorSDG.py (🌟强烈推荐 - 纯Python实现)
**功能特点:**
- 完全使用Python实现的Actor SDG (Synthetic Data Generation)系统
- 不依赖已废弃的扩展，完全兼容Isaac Sim 5.0
- 支持多个Actor同时控制和管理
- 完整的命令队列系统和状态管理
- 实时动画和移动控制

**核心功能:**
- 动态创建和管理多个Actor
- 支持Idle、GoTo、LookAround、Walk命令
- 实时位置更新和动画状态切换
- 命令队列和批量控制
- 完善的UI控制面板

### 2. IsaacSim5_IRA_Animation.py (IRA扩展版本)
**功能特点:**
- 使用Isaac Sim 5.0的IRA (Isaacsim.Replicator.Agent)扩展
- 基于官方推荐的Actor控制方法
- 支持IRA命令格式和语法
- 自动启用必要的扩展

**核心功能:**
- IRA角色生成和控制
- 支持标准IRA命令格式
- 自定义命令执行
- 完善的备用系统

### 3. IsaacSim5AnimationExample.py (基础版本 - 已修复键盘问题)
**功能特点:**
- 专门为Isaac Sim 5.0设计的基础动画控制
- 修复了键盘输入API问题
- 使用属性驱动的动画系统
- 完善的错误处理和备用方案

**核心功能:**
- 自动检测和设置骨骼动画系统
- 支持Idle、Walk、Run动画状态
- 修复的键盘控制系统
- UI控制面板

### 2. SimpleAnimationExample.py (基础示例 - 已更新)
**功能特点:**
- 简化的动画控制示例，已更新为Isaac Sim 5.0兼容
- 使用属性驱动的动画系统
- 支持动画混合权重控制
- 不依赖已废弃的omni.anim.clip节点

**核心功能:**
- 创建动画状态属性
- 使用SkelAnimation进行动画控制
- 实时权重调节和状态切换

### 3. IRAAnimationExample.py (高级版本 - 已更新)
**功能特点:**
- 使用Isaac Sim 5.0的omni.anim.people扩展
- 支持多种动画状态（Idle、Walk、Run）
- 专业的骨骼动画系统集成
- 智能的动画状态管理

**特性:**
- 自动启用omni.anim.people扩展
- 支持SkelRoot和SkelAnimation查找
- 完善的备用动画系统
- 更好的动画资源管理

### 4. CharacterAnimationController.py (完整功能版本)
**注意:** 此脚本使用了已废弃的API，建议使用上述更新版本

## Isaac Sim 5.0 技术实现

### 扩展启用方法
```python
# 启用必要的扩展
extension_manager = omni.kit.app.get_app().get_extension_manager()
extension_manager.set_extension_enabled_immediate("omni.anim.people", True)
```

### 动画属性设置方法
```python
# 设置动画状态属性
if not target_prim.HasAttribute("animationState"):
    target_prim.CreateAttribute("animationState", Sdf.ValueTypeNames.String)
target_prim.GetAttribute("animationState").Set("idle")

# 设置动画速度属性
if not target_prim.HasAttribute("animationSpeed"):
    target_prim.CreateAttribute("animationSpeed", Sdf.ValueTypeNames.Float)
target_prim.GetAttribute("animationSpeed").Set(1.0)
```

### SkelAnimation控制方法
```python
# 查找并控制SkelAnimation
for child in target_prim.GetChildren():
    if child.GetTypeName() == "SkelAnimation":
        if not child.HasAttribute("currentClip"):
            child.CreateAttribute("currentClip", Sdf.ValueTypeNames.String)
        child.GetAttribute("currentClip").Set("walk")
```

## 使用方法

### 运行脚本
```bash
# 🌟强烈推荐：纯Python Actor SDG实现
python Scripts/test/PureActorSDG.py

# IRA扩展版本
python Scripts/test/IsaacSim5_IRA_Animation.py

# 基础版本（已修复键盘问题）
python Scripts/test/IsaacSim5AnimationExample.py

# 其他更新版本
python Scripts/test/SimpleAnimationExample.py
python Scripts/test/IRAAnimationExample.py
```

### 控制方式

#### UI控制
- 所有脚本都提供图形化控制面板
- 支持按钮快速切换动画状态
- 滑块精确调节动画参数

#### 键盘控制
**通用快捷键:**
- `空格键`: 切换Idle/Walking状态
- `1-4键`: 快速切换不同动画状态

**移动控制 (CharacterAnimationController.py):**
- `W`: 向前移动
- `S`: 向后移动
- `A`: 向左移动
- `D`: 向右移动

## 动画状态说明

### 基本动画状态
1. **Idle**: 静止状态，角色保持站立姿势
2. **Walking**: 行走状态，正常步行动画
3. **Running**: 跑步状态（IRA示例中可用）
4. **Wave**: 挥手动画（IRA示例中可用）

### 动画混合
- 支持0.0-1.0之间的权重混合
- 0.0 = 纯Idle动画
- 1.0 = 纯Walking动画
- 中间值 = 混合动画效果

## 技术要点

### 1. 角色模型加载
```python
# 优先尝试加载人物USD模型
character_urls = [
    "https://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/5.0/Isaac/People/Characters/F_Businesss_02/F_Businesss_02.usd",
    "/data/1_LinuxFiles/Simulation/UsdFiles/F_Medical_01/F_Medical_01.usd"
]

# 备用方案：创建简单几何体
capsule = UsdGeom.Capsule.Define(stage, character_path)
```

### 2. 动画图创建
- 使用`omni.graph.core`创建动画节点
- 连接动画剪辑到混合节点
- 设置骨骼动画输出

### 3. 状态管理
- 实时监控动画状态
- 平滑的状态过渡
- 错误处理和备用方案

## 扩展功能

### 可添加的功能
1. **更多动画状态**: 跳跃、蹲下、转身等
2. **动画序列**: 复杂的动画组合
3. **表情控制**: 面部动画
4. **服装变换**: 动态更换角色外观
5. **群体动画**: 多角色协调动画

### 自定义动画
```python
# 添加自定义动画剪辑
def add_custom_animation(clip_name, clip_path):
    og.Controller.edit(
        {"graph_path": graph_path},
        {
            og.Controller.Keys.CREATE_NODES: [
                (f"{clip_name}Clip", "omni.anim.clip"),
            ],
            og.Controller.Keys.SET_VALUES: [
                (f"{clip_name}Clip.inputs:clipPath", clip_path),
            ],
        },
    )
```

## 故障排除

### 常见问题
1. **角色不显示**: 检查USD文件路径和网络连接
2. **动画不播放**: 确认动画图创建成功
3. **UI无响应**: 检查UI事件绑定
4. **键盘控制失效**: 验证键盘事件注册

### 调试技巧
- 查看控制台输出信息
- 检查USD场景图结构
- 验证动画节点连接
- 测试备用角色模型

## 性能优化

### 建议
1. 限制同时活跃的动画数量
2. 使用LOD系统管理远距离角色
3. 优化动画更新频率
4. 合理使用动画缓存

## 总结

这三个示例展示了Isaac Sim中人物动画控制的不同方法和复杂度级别：

- **SimpleAnimationExample.py**: 适合学习基础概念
- **CharacterAnimationController.py**: 适合完整应用开发
- **IRAAnimationExample.py**: 适合专业动画制作

选择合适的示例作为起点，根据项目需求进行扩展和定制。
