import json

def parse_trajectories(json_path):
    with open(json_path, 'r') as f:
        data = json.load(f)

    parsed = {}
    for person, info in data.items():
        traj_list = info.get("trajectory", [])
        parsed[person] = []
        for point in traj_list:
            ts = point.get("timestamp")
            abs_ts = point.get("absolute_timestamp")
            pos = point.get("position")
            if ts is None or abs_ts is None or pos is None:
                print(f"Skipping incomplete point for {person}: {point}")
                continue
            parsed[person].append({
                "timestamp": ts,
                "absolute_timestamp": abs_ts,
                "position": pos
            })
    return parsed

def main():
    json_file = "/data/1_LinuxFiles/Simulation/Yanglaoyuan_LLM/Scripts/test/trajectories.json"
    trajectories = parse_trajectories(json_file)
    
    for person, traj in trajectories.items():
        print(f"== Trajectory for {person} ==")
        for idx, pt in enumerate(traj):
            print(
                f" Point {idx}: "
                f"timestamp = {pt['timestamp']}, "
                f"absolute_timestamp = {pt['absolute_timestamp']}, "
                f"position = {pt['position']}"
            )
        print()

if __name__ == "__main__":
    main()
